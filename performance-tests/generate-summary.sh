#!/bin/bash

# Script de génération de rapport de synthèse des tests de performance
# Au<PERSON><PERSON>: Assistant IA

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPORTS_DIR="$SCRIPT_DIR/reports"

# Fonction d'affichage du header
show_header() {
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║${NC}${GREEN}                    RAPPORT DE SYNTHÈSE                      ${NC}${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}${GREEN}              Tests de Performance EcoTask                   ${NC}${BLUE}║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Fonction d'analyse d'un fichier de résultats
analyze_results() {
    local results_file="$1"
    local test_name="$2"
    
    if [ ! -f "$results_file" ]; then
        echo -e "${RED}❌ Fichier non trouvé: $results_file${NC}"
        return 1
    fi
    
    echo -e "${CYAN}📊 Analyse: $test_name${NC}"
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    
    # Compter les requêtes
    local total_requests=$(tail -n +2 "$results_file" | wc -l)
    local successful_requests=$(tail -n +2 "$results_file" | awk -F',' '$8=="true"' | wc -l)
    local failed_requests=$((total_requests - successful_requests))
    
    # Calculer les temps de réponse
    local avg_response_time=$(tail -n +2 "$results_file" | awk -F',' '{sum+=$2; count++} END {if(count>0) print int(sum/count); else print 0}')
    local min_response_time=$(tail -n +2 "$results_file" | awk -F',' 'NR==2{min=$2} {if($2<min) min=$2} END {print min}')
    local max_response_time=$(tail -n +2 "$results_file" | awk -F',' '{if($2>max) max=$2} END {print max}')
    
    # Calculer le 95e percentile
    local p95_response_time=$(tail -n +2 "$results_file" | awk -F',' '{print $2}' | sort -n | awk '{all[NR] = $0} END{print all[int(NR*0.95)]}')
    
    # Calculer le débit
    local first_timestamp=$(tail -n +2 "$results_file" | head -n1 | awk -F',' '{print $1}')
    local last_timestamp=$(tail -n +2 "$results_file" | tail -n1 | awk -F',' '{print $1}')
    local duration_ms=$((last_timestamp - first_timestamp))
    local duration_sec=$((duration_ms / 1000))
    local throughput=0
    if [ $duration_sec -gt 0 ]; then
        throughput=$(echo "scale=2; $total_requests / $duration_sec" | bc -l 2>/dev/null || echo "N/A")
    fi
    
    # Calculer le taux d'erreur
    local error_rate=0
    if [ $total_requests -gt 0 ]; then
        error_rate=$(echo "scale=2; $failed_requests * 100 / $total_requests" | bc -l 2>/dev/null || echo "0")
    fi
    
    # Affichage des résultats
    echo -e "${GREEN}📈 Métriques Générales:${NC}"
    echo "  • Total des requêtes      : $total_requests"
    echo "  • Requêtes réussies       : $successful_requests"
    echo "  • Requêtes échouées       : $failed_requests"
    echo "  • Durée du test           : ${duration_sec}s"
    echo "  • Débit                   : ${throughput} req/s"
    
    echo ""
    echo -e "${YELLOW}⏱️  Temps de Réponse:${NC}"
    echo "  • Temps moyen             : ${avg_response_time}ms"
    echo "  • Temps minimum           : ${min_response_time}ms"
    echo "  • Temps maximum           : ${max_response_time}ms"
    echo "  • 95e percentile          : ${p95_response_time}ms"
    
    echo ""
    echo -e "${PURPLE}🎯 Qualité:${NC}"
    if [ "$error_rate" = "0" ] || [ "$error_rate" = "0.00" ]; then
        echo -e "  • Taux d'erreur           : ${GREEN}${error_rate}% ✅${NC}"
    elif (( $(echo "$error_rate < 1" | bc -l 2>/dev/null || echo 0) )); then
        echo -e "  • Taux d'erreur           : ${YELLOW}${error_rate}% ⚠️${NC}"
    else
        echo -e "  • Taux d'erreur           : ${RED}${error_rate}% ❌${NC}"
    fi
    
    # Évaluation des performances
    echo ""
    echo -e "${CYAN}🏆 Évaluation:${NC}"
    
    if [ "$avg_response_time" -lt 500 ]; then
        echo -e "  • Performance             : ${GREEN}Excellente ⭐⭐⭐${NC}"
    elif [ "$avg_response_time" -lt 1000 ]; then
        echo -e "  • Performance             : ${GREEN}Bonne ⭐⭐${NC}"
    elif [ "$avg_response_time" -lt 2000 ]; then
        echo -e "  • Performance             : ${YELLOW}Acceptable ⭐${NC}"
    else
        echo -e "  • Performance             : ${RED}Problématique ❌${NC}"
    fi
    
    if [ "$max_response_time" -lt 1000 ]; then
        echo -e "  • Stabilité               : ${GREEN}Excellente ⭐⭐⭐${NC}"
    elif [ "$max_response_time" -lt 3000 ]; then
        echo -e "  • Stabilité               : ${GREEN}Bonne ⭐⭐${NC}"
    elif [ "$max_response_time" -lt 5000 ]; then
        echo -e "  • Stabilité               : ${YELLOW}Acceptable ⭐${NC}"
    else
        echo -e "  • Stabilité               : ${RED}Problématique ❌${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo ""
}

# Fonction de recommandations
show_recommendations() {
    echo -e "${GREEN}💡 Recommandations:${NC}"
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    
    echo "🔧 Optimisations suggérées:"
    echo "  • Mettre en place un cache Redis pour améliorer les performances"
    echo "  • Optimiser les requêtes de base de données avec des index"
    echo "  • Considérer l'utilisation d'un CDN pour les assets statiques"
    echo "  • Implémenter la compression gzip/brotli"
    echo "  • Monitorer les performances en production avec APM"
    
    echo ""
    echo "📊 Surveillance continue:"
    echo "  • Configurer des alertes sur les temps de réponse > 2s"
    echo "  • Monitorer l'utilisation CPU/Mémoire"
    echo "  • Mettre en place des tests de performance automatisés"
    echo "  • Effectuer des tests de charge réguliers"
    
    echo ""
    echo "🎯 Objectifs de performance recommandés:"
    echo "  • Temps de réponse moyen < 500ms"
    echo "  • 95e percentile < 1000ms"
    echo "  • Taux d'erreur < 0.1%"
    echo "  • Capacité > 100 utilisateurs simultanés"
    
    echo ""
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
}

# Fonction principale
main() {
    show_header
    
    echo -e "${YELLOW}🔍 Recherche des fichiers de résultats...${NC}"
    echo ""
    
    # Trouver tous les fichiers de résultats
    local results_files=($(find "$REPORTS_DIR" -name "results_*.jtl" -type f | sort -r))
    
    if [ ${#results_files[@]} -eq 0 ]; then
        echo -e "${RED}❌ Aucun fichier de résultats trouvé dans $REPORTS_DIR${NC}"
        echo "Veuillez d'abord exécuter des tests de performance."
        exit 1
    fi
    
    echo -e "${GREEN}✅ ${#results_files[@]} fichier(s) de résultats trouvé(s)${NC}"
    echo ""
    
    # Analyser chaque fichier de résultats
    local count=1
    for results_file in "${results_files[@]}"; do
        local filename=$(basename "$results_file")
        local timestamp=$(echo "$filename" | sed 's/results_\(.*\)\.jtl/\1/')
        local test_name="Test #$count ($timestamp)"
        
        analyze_results "$results_file" "$test_name"
        ((count++))
    done
    
    show_recommendations
    
    echo -e "${GREEN}📁 Rapports HTML disponibles:${NC}"
    echo "  • Dernier rapport: $REPORTS_DIR/latest-report/index.html"
    echo "  • Tous les rapports: $REPORTS_DIR/html-report_*/index.html"
    echo ""
    
    echo -e "${BLUE}🎉 Analyse terminée !${NC}"
}

# Exécution du script principal
main "$@"
