# 🚀 Tests de Performance EcoTask avec Apache JMeter

## 📋 Vue d'ensemble

Ce dossier contient tous les outils nécessaires pour effectuer des tests de performance complets sur l'application EcoTask Symfony avec Apache JMeter.

## 🎯 Objectifs des tests

- **Mesurer les temps de réponse** des pages principales
- **Évaluer la capacité de montée en charge** (scalabilité)
- **Identifier les goulots d'étranglement** de performance
- **Valider la stabilité** sous charge
- **Générer des rapports HTML détaillés**

## 📁 Structure des fichiers

```
performance-tests/
├── README.md                    # Ce fichier
├── run-performance-tests.sh     # Script principal d'exécution
├── test-config.properties       # Configuration des tests
├── ecotask-load-test.jmx        # Plan de test de charge standard
├── ecotask-stress-test.jmx      # Plan de test de stress intensif
└── reports/                     # Dossier des rapports générés
    ├── results_*.jtl           # Fichiers de résultats bruts
    ├── html-report_*/          # Rapports HTML détaillés
    └── latest-report/          # Lien vers le dernier rapport
```

## 🛠️ Prérequis

### Logiciels requis
- **Java 11+** (pour JMeter)
- **Apache JMeter 5.6.3+**
- **Bash** (pour les scripts)
- **curl** (pour les tests de connectivité)

### Installation JMeter
```bash
# JMeter est déjà installé dans /opt/jmeter
# Vérification
jmeter --version
```

## 🚀 Utilisation

### 1. Test de performance standard

```bash
# Lancer les tests avec les paramètres par défaut
./run-performance-tests.sh

# Avec paramètres personnalisés
./run-performance-tests.sh -s localhost -p 8000
```

### 2. Test de stress intensif

```bash
# Utiliser le plan de test de stress
./run-performance-tests.sh -t ecotask-stress-test.jmx
```

### 3. Options disponibles

```bash
./run-performance-tests.sh --help
```

**Options principales :**
- `-s, --server HOST` : Serveur cible (défaut: localhost)
- `-p, --port PORT` : Port du serveur (défaut: 8000)
- `-t, --test-plan FILE` : Plan de test JMeter
- `-r, --report-only` : Générer uniquement le rapport HTML
- `-c, --clean` : Nettoyer les anciens rapports

## 📊 Plans de test disponibles

### 1. Test de charge standard (`ecotask-load-test.jmx`)

**Scénarios :**
- **Groupe 1** : 10 utilisateurs légers (navigation simple)
- **Groupe 2** : 50 utilisateurs intensifs (scénario complet)

**Pages testées :**
- `/` - Page d'accueil/Dashboard
- `/health` - Health check
- `/project/` - Liste des projets
- `/task/` - Liste des tâches

**Durée :** ~10 minutes

### 2. Test de stress (`ecotask-stress-test.jmx`)

**Phases progressives :**
- **Phase 1** : 25 utilisateurs (5 min)
- **Phase 2** : 50 utilisateurs (10 min)

**Objectif :** Identifier le point de rupture

## 📈 Interprétation des résultats

### Métriques clés à surveiller

1. **Temps de réponse moyen** : < 2000ms acceptable
2. **Temps de réponse 95e percentile** : < 5000ms acceptable
3. **Taux d'erreur** : < 1% excellent, < 5% acceptable
4. **Débit** (requêtes/seconde) : Plus élevé = mieux
5. **Utilisation CPU/Mémoire** : < 80% recommandé

### Seuils de performance

| Métrique | Excellent | Bon | Acceptable | Problématique |
|----------|-----------|-----|------------|---------------|
| Temps de réponse moyen | < 500ms | < 1000ms | < 2000ms | > 2000ms |
| Taux d'erreur | 0% | < 0.1% | < 1% | > 1% |
| CPU | < 50% | < 70% | < 80% | > 80% |
| Mémoire | < 60% | < 75% | < 85% | > 85% |

## 📋 Rapports générés

### 1. Rapport HTML interactif
- **Localisation** : `reports/latest-report/index.html`
- **Contenu** : Graphiques, tableaux, statistiques détaillées
- **Navigation** : Interface web complète

### 2. Fichiers de résultats bruts
- **Format** : `.jtl` (JMeter Test Log)
- **Utilisation** : Analyse personnalisée, import dans d'autres outils

## 🔧 Configuration avancée

### Personnalisation des tests

Éditez `test-config.properties` pour ajuster :
- Nombre d'utilisateurs virtuels
- Durée des tests
- Temps de montée en charge
- Seuils de performance

### Variables d'environnement

```bash
export SERVER_HOST=production.ecotask.com
export SERVER_PORT=443
export PROTOCOL=https
./run-performance-tests.sh
```

## 🐛 Résolution de problèmes

### Problèmes courants

1. **"JMeter non trouvé"**
   ```bash
   # Vérifier l'installation
   which jmeter
   # Ajuster le chemin dans le script si nécessaire
   ```

2. **"Serveur non accessible"**
   ```bash
   # Vérifier que l'application EcoTask est démarrée
   curl http://localhost:8000/health
   ```

3. **"Erreurs de timeout"**
   - Augmenter les timeouts dans la configuration
   - Réduire le nombre d'utilisateurs virtuels

4. **"Mémoire insuffisante"**
   ```bash
   # Augmenter la mémoire JMeter
   export JVM_ARGS="-Xms1024m -Xmx2048m"
   ```

## 📊 Exemples de commandes

```bash
# Test rapide avec 5 utilisateurs
./run-performance-tests.sh -s localhost -p 8000

# Test de stress complet
./run-performance-tests.sh -t ecotask-stress-test.jmx

# Générer un nouveau rapport depuis les résultats existants
./run-performance-tests.sh --report-only

# Nettoyer tous les anciens rapports
./run-performance-tests.sh --clean

# Test sur un serveur distant
./run-performance-tests.sh -s production.ecotask.com -p 443
```

## 📝 Bonnes pratiques

1. **Avant les tests :**
   - Vérifier que l'application est stable
   - S'assurer d'avoir des données de test cohérentes
   - Monitorer les ressources système

2. **Pendant les tests :**
   - Surveiller les logs de l'application
   - Vérifier l'utilisation CPU/Mémoire
   - Noter les comportements anormaux

3. **Après les tests :**
   - Analyser les rapports HTML
   - Identifier les pages les plus lentes
   - Documenter les résultats et recommandations

## 🎯 Objectifs de performance recommandés

Pour l'application EcoTask :
- **Dashboard** : < 1000ms (page principale)
- **Listes** (projets/tâches) : < 1500ms
- **Health check** : < 100ms
- **Taux d'erreur global** : < 0.5%
- **Capacité** : 50+ utilisateurs simultanés

## 📞 Support

En cas de problème avec les tests de performance :
1. Vérifier les logs dans `reports/`
2. Consulter la documentation JMeter officielle
3. Ajuster les paramètres selon l'environnement
