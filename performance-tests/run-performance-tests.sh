#!/bin/bash

# Script de test de performance pour EcoTask avec Apache JMeter
# Auteur: Assistant IA
# Date: $(date)

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REPORTS_DIR="$SCRIPT_DIR/reports"
JMETER_HOME="/opt/jmeter"
JMETER_BIN="$JMETER_HOME/bin/jmeter"

# Variables par défaut
SERVER_HOST="localhost"
SERVER_PORT="8000"
PROTOCOL="http"
TEST_PLAN="$SCRIPT_DIR/ecotask-load-test.jmx"

# Fonction d'aide
show_help() {
    echo -e "${BLUE}🚀 Script de Test de Performance EcoTask${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Afficher cette aide"
    echo "  -s, --server HOST       Serveur cible (défaut: localhost)"
    echo "  -p, --port PORT         Port du serveur (défaut: 8000)"
    echo "  -t, --test-plan FILE    Plan de test JMeter (défaut: ecotask-load-test.jmx)"
    echo "  -r, --report-only       Générer uniquement le rapport HTML"
    echo "  -c, --clean             Nettoyer les anciens rapports"
    echo ""
    echo "Exemples:"
    echo "  $0                      # Test avec paramètres par défaut"
    echo "  $0 -s production.com -p 443"
    echo "  $0 --clean              # Nettoyer les rapports"
    echo "  $0 --report-only        # Générer rapport depuis résultats existants"
}

# Fonction de nettoyage
clean_reports() {
    echo -e "${YELLOW}🧹 Nettoyage des anciens rapports...${NC}"
    rm -rf "$REPORTS_DIR"/*.jtl
    rm -rf "$REPORTS_DIR"/*.html
    rm -rf "$REPORTS_DIR"/html-report
    echo -e "${GREEN}✅ Nettoyage terminé${NC}"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    echo -e "${BLUE}🔍 Vérification des prérequis...${NC}"
    
    # Vérifier JMeter
    if [ ! -f "$JMETER_BIN" ]; then
        echo -e "${RED}❌ JMeter non trouvé dans $JMETER_HOME${NC}"
        echo "Veuillez installer JMeter ou ajuster JMETER_HOME"
        exit 1
    fi
    
    # Vérifier Java
    if ! command -v java &> /dev/null; then
        echo -e "${RED}❌ Java non trouvé${NC}"
        echo "Veuillez installer Java"
        exit 1
    fi
    
    # Vérifier le plan de test
    if [ ! -f "$TEST_PLAN" ]; then
        echo -e "${RED}❌ Plan de test non trouvé: $TEST_PLAN${NC}"
        exit 1
    fi
    
    # Créer le dossier de rapports
    mkdir -p "$REPORTS_DIR"
    
    echo -e "${GREEN}✅ Prérequis vérifiés${NC}"
}

# Fonction de test de connectivité
test_connectivity() {
    echo -e "${BLUE}🌐 Test de connectivité vers $PROTOCOL://$SERVER_HOST:$SERVER_PORT${NC}"
    
    if command -v curl &> /dev/null; then
        if curl -s --connect-timeout 5 "$PROTOCOL://$SERVER_HOST:$SERVER_PORT/health" > /dev/null; then
            echo -e "${GREEN}✅ Serveur accessible${NC}"
        else
            echo -e "${YELLOW}⚠️  Serveur non accessible, test continuera quand même${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  curl non disponible, impossible de tester la connectivité${NC}"
    fi
}

# Fonction d'exécution des tests
run_tests() {
    echo -e "${BLUE}🚀 Démarrage des tests de performance...${NC}"
    echo -e "${YELLOW}Configuration:${NC}"
    echo "  - Serveur: $PROTOCOL://$SERVER_HOST:$SERVER_PORT"
    echo "  - Plan de test: $TEST_PLAN"
    echo "  - Rapports: $REPORTS_DIR"
    echo ""
    
    # Timestamp pour les fichiers
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    RESULTS_FILE="$REPORTS_DIR/results_$TIMESTAMP.jtl"
    
    # Commande JMeter
    JMETER_CMD="$JMETER_BIN -n -t $TEST_PLAN \
        -JSERVER_HOST=$SERVER_HOST \
        -JSERVER_PORT=$SERVER_PORT \
        -JPROTOCOL=$PROTOCOL \
        -l $RESULTS_FILE \
        -e -o $REPORTS_DIR/html-report_$TIMESTAMP"
    
    echo -e "${BLUE}📊 Exécution de JMeter...${NC}"
    echo "Commande: $JMETER_CMD"
    echo ""
    
    # Exécuter JMeter
    if $JMETER_CMD; then
        echo -e "${GREEN}✅ Tests terminés avec succès${NC}"
        echo -e "${BLUE}📈 Rapport HTML généré: $REPORTS_DIR/html-report_$TIMESTAMP/index.html${NC}"
        
        # Créer un lien symbolique vers le dernier rapport
        ln -sf "html-report_$TIMESTAMP" "$REPORTS_DIR/latest-report"
        echo -e "${GREEN}🔗 Lien vers le dernier rapport: $REPORTS_DIR/latest-report/index.html${NC}"
        
        # Afficher un résumé
        show_summary "$RESULTS_FILE"
        
    else
        echo -e "${RED}❌ Erreur lors de l'exécution des tests${NC}"
        exit 1
    fi
}

# Fonction d'affichage du résumé
show_summary() {
    local results_file="$1"
    
    if [ -f "$results_file" ]; then
        echo -e "${BLUE}📊 Résumé des résultats:${NC}"
        
        # Compter les requêtes
        local total_requests=$(tail -n +2 "$results_file" | wc -l)
        local successful_requests=$(tail -n +2 "$results_file" | awk -F',' '$8=="true"' | wc -l)
        local failed_requests=$((total_requests - successful_requests))
        
        # Calculer les temps de réponse moyens
        local avg_response_time=$(tail -n +2 "$results_file" | awk -F',' '{sum+=$2; count++} END {if(count>0) print int(sum/count); else print 0}')
        
        echo "  - Total des requêtes: $total_requests"
        echo "  - Requêtes réussies: $successful_requests"
        echo "  - Requêtes échouées: $failed_requests"
        echo "  - Temps de réponse moyen: ${avg_response_time}ms"
        
        if [ $failed_requests -gt 0 ]; then
            local error_rate=$(echo "scale=2; $failed_requests * 100 / $total_requests" | bc -l 2>/dev/null || echo "N/A")
            echo -e "  - Taux d'erreur: ${RED}${error_rate}%${NC}"
        else
            echo -e "  - Taux d'erreur: ${GREEN}0%${NC}"
        fi
    fi
}

# Fonction de génération de rapport uniquement
generate_report_only() {
    echo -e "${BLUE}📊 Génération du rapport HTML uniquement...${NC}"
    
    # Trouver le fichier de résultats le plus récent
    LATEST_RESULTS=$(ls -t "$REPORTS_DIR"/*.jtl 2>/dev/null | head -n1)
    
    if [ -z "$LATEST_RESULTS" ]; then
        echo -e "${RED}❌ Aucun fichier de résultats trouvé${NC}"
        exit 1
    fi
    
    echo "Utilisation des résultats: $LATEST_RESULTS"
    
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    REPORT_DIR="$REPORTS_DIR/html-report_$TIMESTAMP"
    
    # Générer le rapport HTML
    if "$JMETER_BIN" -g "$LATEST_RESULTS" -o "$REPORT_DIR"; then
        echo -e "${GREEN}✅ Rapport HTML généré: $REPORT_DIR/index.html${NC}"
        ln -sf "html-report_$TIMESTAMP" "$REPORTS_DIR/latest-report"
    else
        echo -e "${RED}❌ Erreur lors de la génération du rapport${NC}"
        exit 1
    fi
}

# Fonction principale
main() {
    local report_only=false
    local clean_only=false
    
    # Traitement des arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--server)
                SERVER_HOST="$2"
                shift 2
                ;;
            -p|--port)
                SERVER_PORT="$2"
                shift 2
                ;;
            -t|--test-plan)
                TEST_PLAN="$2"
                shift 2
                ;;
            -r|--report-only)
                report_only=true
                shift
                ;;
            -c|--clean)
                clean_only=true
                shift
                ;;
            *)
                echo -e "${RED}❌ Option inconnue: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Affichage du header
    echo -e "${GREEN}🚀 EcoTask - Tests de Performance avec Apache JMeter${NC}"
    echo -e "${BLUE}=================================================${NC}"
    echo ""
    
    # Exécution selon les options
    if [ "$clean_only" = true ]; then
        clean_reports
        exit 0
    fi
    
    check_prerequisites
    
    if [ "$report_only" = true ]; then
        generate_report_only
    else
        test_connectivity
        run_tests
    fi
    
    echo ""
    echo -e "${GREEN}🎉 Terminé !${NC}"
    echo -e "${BLUE}📁 Consultez les rapports dans: $REPORTS_DIR${NC}"
}

# Exécution du script principal
main "$@"
