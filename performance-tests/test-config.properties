# Configuration des tests de performance EcoTask
# Fichier de propriétés pour JMeter

# Configuration du serveur cible
SERVER_HOST=localhost
SERVER_PORT=8000
PROTOCOL=http

# Configuration des utilisateurs virtuels
LIGHT_USERS=10
MEDIUM_USERS=25
HEAVY_USERS=50
STRESS_USERS=100

# Durées des tests (en secondes)
LIGHT_DURATION=180
MEDIUM_DURATION=300
HEAVY_DURATION=600
STRESS_DURATION=900

# Temps de montée en charge (ramp-up en secondes)
LIGHT_RAMP_UP=30
MEDIUM_RAMP_UP=60
HEAVY_RAMP_UP=120
STRESS_RAMP_UP=300

# Nombre de boucles par utilisateur
LIGHT_LOOPS=3
MEDIUM_LOOPS=5
HEAVY_LOOPS=10
STRESS_LOOPS=20

# Temps de réflexion (think time) en millisecondes
MIN_THINK_TIME=1000
MAX_THINK_TIME=5000

# Timeouts (en millisecondes)
CONNECT_TIMEOUT=10000
RESPONSE_TIMEOUT=30000

# Configuration des rapports
REPORT_TITLE=EcoTask Performance Test Report
REPORT_DESCRIPTION=Tests de performance pour l'application EcoTask Symfony

# Seuils de performance (en millisecondes)
ACCEPTABLE_RESPONSE_TIME=2000
GOOD_RESPONSE_TIME=1000
EXCELLENT_RESPONSE_TIME=500

# Seuils d'erreur (en pourcentage)
MAX_ERROR_RATE=5.0
ACCEPTABLE_ERROR_RATE=1.0

# Configuration JMeter
JMETER_HEAP_SIZE=1024m
JMETER_NEW_SIZE=512m

# Endpoints à tester
ENDPOINTS=/,/dashboard,/health,/project/,/task/

# Types de tests disponibles
TEST_TYPES=light,medium,heavy,stress

# Configuration des assertions
CHECK_RESPONSE_CODE=true
CHECK_RESPONSE_TIME=true
CHECK_RESPONSE_SIZE=false

# Configuration du monitoring
MONITOR_CPU=true
MONITOR_MEMORY=true
MONITOR_NETWORK=false

# Paramètres avancés
USE_KEEPALIVE=true
USE_CACHE=true
FOLLOW_REDIRECTS=true

# Configuration des cookies
CLEAR_COOKIES_EACH_ITERATION=false

# Configuration SSL (si nécessaire)
SSL_VERIFY=false
SSL_PROTOCOL=TLS

# Paramètres de debug
DEBUG_MODE=false
VERBOSE_LOGGING=false

# Configuration des threads
MAX_THREAD_POOL_SIZE=200
THREAD_POOL_CORE_SIZE=50

# Configuration des résultats
SAVE_RESPONSE_DATA=false
SAVE_REQUEST_HEADERS=false
SAVE_RESPONSE_HEADERS=false
SAVE_ASSERTIONS=true

# Format des timestamps
TIMESTAMP_FORMAT=yyyy-MM-dd HH:mm:ss

# Configuration des graphiques
GRAPH_WIDTH=800
GRAPH_HEIGHT=600

# Paramètres de performance système
SYSTEM_CPU_THRESHOLD=80
SYSTEM_MEMORY_THRESHOLD=85
SYSTEM_DISK_THRESHOLD=90

# Configuration des alertes
ALERT_ON_ERROR_RATE=true
ALERT_ON_RESPONSE_TIME=true
ALERT_ON_SYSTEM_RESOURCES=true

# Email de notification (si configuré)
NOTIFICATION_EMAIL=<EMAIL>

# Configuration des données de test
USE_TEST_DATA=true
TEST_DATA_FILE=test-data.csv

# Paramètres de simulation utilisateur
SIMULATE_REAL_USERS=true
USER_BEHAVIOR_VARIATION=20

# Configuration de la base de données de test
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_NAME=ecotask_db
TEST_DB_USER=root
TEST_DB_PASSWORD=MLKqsd002
