/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 45.0, "minX": 0.0, "maxY": 1180.0, "series": [{"data": [[0.0, 45.0], [0.1, 45.0], [0.2, 45.0], [0.3, 45.0], [0.4, 45.0], [0.5, 45.0], [0.6, 45.0], [0.7, 45.0], [0.8, 45.0], [0.9, 45.0], [1.0, 46.0], [1.1, 46.0], [1.2, 46.0], [1.3, 46.0], [1.4, 46.0], [1.5, 46.0], [1.6, 46.0], [1.7, 46.0], [1.8, 46.0], [1.9, 46.0], [2.0, 46.0], [2.1, 46.0], [2.2, 46.0], [2.3, 46.0], [2.4, 46.0], [2.5, 46.0], [2.6, 46.0], [2.7, 46.0], [2.8, 46.0], [2.9, 46.0], [3.0, 46.0], [3.1, 46.0], [3.2, 46.0], [3.3, 46.0], [3.4, 46.0], [3.5, 46.0], [3.6, 46.0], [3.7, 46.0], [3.8, 46.0], [3.9, 46.0], [4.0, 46.0], [4.1, 46.0], [4.2, 46.0], [4.3, 46.0], [4.4, 46.0], [4.5, 46.0], [4.6, 46.0], [4.7, 46.0], [4.8, 46.0], [4.9, 46.0], [5.0, 46.0], [5.1, 46.0], [5.2, 46.0], [5.3, 46.0], [5.4, 46.0], [5.5, 46.0], [5.6, 46.0], [5.7, 46.0], [5.8, 46.0], [5.9, 46.0], [6.0, 46.0], [6.1, 46.0], [6.2, 46.0], [6.3, 46.0], [6.4, 46.0], [6.5, 46.0], [6.6, 46.0], [6.7, 46.0], [6.8, 46.0], [6.9, 47.0], [7.0, 47.0], [7.1, 47.0], [7.2, 47.0], [7.3, 47.0], [7.4, 47.0], [7.5, 47.0], [7.6, 47.0], [7.7, 47.0], [7.8, 47.0], [7.9, 47.0], [8.0, 47.0], [8.1, 47.0], [8.2, 47.0], [8.3, 47.0], [8.4, 47.0], [8.5, 47.0], [8.6, 47.0], [8.7, 47.0], [8.8, 47.0], [8.9, 47.0], [9.0, 47.0], [9.1, 47.0], [9.2, 47.0], [9.3, 47.0], [9.4, 47.0], [9.5, 47.0], [9.6, 47.0], [9.7, 47.0], [9.8, 47.0], [9.9, 47.0], [10.0, 47.0], [10.1, 47.0], [10.2, 47.0], [10.3, 47.0], [10.4, 47.0], [10.5, 47.0], [10.6, 47.0], [10.7, 47.0], [10.8, 47.0], [10.9, 47.0], [11.0, 47.0], [11.1, 47.0], [11.2, 47.0], [11.3, 47.0], [11.4, 47.0], [11.5, 47.0], [11.6, 47.0], [11.7, 47.0], [11.8, 47.0], [11.9, 47.0], [12.0, 47.0], [12.1, 47.0], [12.2, 47.0], [12.3, 47.0], [12.4, 47.0], [12.5, 47.0], [12.6, 47.0], [12.7, 47.0], [12.8, 47.0], [12.9, 47.0], [13.0, 47.0], [13.1, 47.0], [13.2, 47.0], [13.3, 47.0], [13.4, 47.0], [13.5, 47.0], [13.6, 47.0], [13.7, 47.0], [13.8, 47.0], [13.9, 47.0], [14.0, 47.0], [14.1, 47.0], [14.2, 47.0], [14.3, 47.0], [14.4, 47.0], [14.5, 47.0], [14.6, 47.0], [14.7, 47.0], [14.8, 47.0], [14.9, 47.0], [15.0, 47.0], [15.1, 47.0], [15.2, 47.0], [15.3, 47.0], [15.4, 47.0], [15.5, 47.0], [15.6, 47.0], [15.7, 47.0], [15.8, 47.0], [15.9, 47.0], [16.0, 47.0], [16.1, 47.0], [16.2, 47.0], [16.3, 48.0], [16.4, 48.0], [16.5, 48.0], [16.6, 48.0], [16.7, 48.0], [16.8, 48.0], [16.9, 48.0], [17.0, 48.0], [17.1, 48.0], [17.2, 48.0], [17.3, 48.0], [17.4, 48.0], [17.5, 48.0], [17.6, 48.0], [17.7, 48.0], [17.8, 48.0], [17.9, 48.0], [18.0, 48.0], [18.1, 48.0], [18.2, 48.0], [18.3, 48.0], [18.4, 48.0], [18.5, 48.0], [18.6, 48.0], [18.7, 48.0], [18.8, 48.0], [18.9, 48.0], [19.0, 48.0], [19.1, 48.0], [19.2, 48.0], [19.3, 48.0], [19.4, 48.0], [19.5, 48.0], [19.6, 48.0], [19.7, 48.0], [19.8, 48.0], [19.9, 48.0], [20.0, 48.0], [20.1, 48.0], [20.2, 48.0], [20.3, 48.0], [20.4, 48.0], [20.5, 48.0], [20.6, 48.0], [20.7, 48.0], [20.8, 48.0], [20.9, 48.0], [21.0, 48.0], [21.1, 48.0], [21.2, 48.0], [21.3, 48.0], [21.4, 48.0], [21.5, 48.0], [21.6, 48.0], [21.7, 48.0], [21.8, 48.0], [21.9, 48.0], [22.0, 48.0], [22.1, 48.0], [22.2, 48.0], [22.3, 48.0], [22.4, 48.0], [22.5, 48.0], [22.6, 48.0], [22.7, 48.0], [22.8, 48.0], [22.9, 48.0], [23.0, 48.0], [23.1, 48.0], [23.2, 48.0], [23.3, 48.0], [23.4, 48.0], [23.5, 48.0], [23.6, 48.0], [23.7, 48.0], [23.8, 48.0], [23.9, 48.0], [24.0, 48.0], [24.1, 48.0], [24.2, 48.0], [24.3, 48.0], [24.4, 48.0], [24.5, 48.0], [24.6, 48.0], [24.7, 48.0], [24.8, 48.0], [24.9, 48.0], [25.0, 48.0], [25.1, 48.0], [25.2, 48.0], [25.3, 48.0], [25.4, 48.0], [25.5, 48.0], [25.6, 48.0], [25.7, 48.0], [25.8, 48.0], [25.9, 48.0], [26.0, 48.0], [26.1, 48.0], [26.2, 48.0], [26.3, 48.0], [26.4, 48.0], [26.5, 48.0], [26.6, 48.0], [26.7, 48.0], [26.8, 48.0], [26.9, 48.0], [27.0, 48.0], [27.1, 48.0], [27.2, 48.0], [27.3, 48.0], [27.4, 48.0], [27.5, 48.0], [27.6, 48.0], [27.7, 48.0], [27.8, 48.0], [27.9, 48.0], [28.0, 48.0], [28.1, 48.0], [28.2, 48.0], [28.3, 48.0], [28.4, 48.0], [28.5, 48.0], [28.6, 48.0], [28.7, 48.0], [28.8, 48.0], [28.9, 48.0], [29.0, 48.0], [29.1, 48.0], [29.2, 48.0], [29.3, 48.0], [29.4, 48.0], [29.5, 48.0], [29.6, 48.0], [29.7, 48.0], [29.8, 48.0], [29.9, 48.0], [30.0, 48.0], [30.1, 48.0], [30.2, 48.0], [30.3, 48.0], [30.4, 48.0], [30.5, 48.0], [30.6, 48.0], [30.7, 48.0], [30.8, 48.0], [30.9, 48.0], [31.0, 48.0], [31.1, 48.0], [31.2, 48.0], [31.3, 48.0], [31.4, 48.0], [31.5, 48.0], [31.6, 48.0], [31.7, 48.0], [31.8, 48.0], [31.9, 48.0], [32.0, 48.0], [32.1, 48.0], [32.2, 48.0], [32.3, 48.0], [32.4, 48.0], [32.5, 48.0], [32.6, 49.0], [32.7, 49.0], [32.8, 49.0], [32.9, 49.0], [33.0, 49.0], [33.1, 49.0], [33.2, 49.0], [33.3, 49.0], [33.4, 49.0], [33.5, 49.0], [33.6, 49.0], [33.7, 49.0], [33.8, 49.0], [33.9, 49.0], [34.0, 49.0], [34.1, 49.0], [34.2, 49.0], [34.3, 49.0], [34.4, 49.0], [34.5, 49.0], [34.6, 49.0], [34.7, 49.0], [34.8, 49.0], [34.9, 49.0], [35.0, 49.0], [35.1, 49.0], [35.2, 49.0], [35.3, 49.0], [35.4, 49.0], [35.5, 49.0], [35.6, 49.0], [35.7, 49.0], [35.8, 49.0], [35.9, 49.0], [36.0, 49.0], [36.1, 49.0], [36.2, 49.0], [36.3, 49.0], [36.4, 49.0], [36.5, 49.0], [36.6, 49.0], [36.7, 49.0], [36.8, 49.0], [36.9, 49.0], [37.0, 49.0], [37.1, 49.0], [37.2, 49.0], [37.3, 49.0], [37.4, 49.0], [37.5, 49.0], [37.6, 49.0], [37.7, 49.0], [37.8, 49.0], [37.9, 49.0], [38.0, 49.0], [38.1, 49.0], [38.2, 49.0], [38.3, 49.0], [38.4, 49.0], [38.5, 49.0], [38.6, 49.0], [38.7, 49.0], [38.8, 49.0], [38.9, 49.0], [39.0, 49.0], [39.1, 49.0], [39.2, 49.0], [39.3, 49.0], [39.4, 49.0], [39.5, 49.0], [39.6, 49.0], [39.7, 49.0], [39.8, 49.0], [39.9, 49.0], [40.0, 49.0], [40.1, 49.0], [40.2, 49.0], [40.3, 49.0], [40.4, 49.0], [40.5, 49.0], [40.6, 49.0], [40.7, 49.0], [40.8, 49.0], [40.9, 49.0], [41.0, 49.0], [41.1, 49.0], [41.2, 49.0], [41.3, 49.0], [41.4, 49.0], [41.5, 49.0], [41.6, 49.0], [41.7, 49.0], [41.8, 49.0], [41.9, 49.0], [42.0, 49.0], [42.1, 49.0], [42.2, 49.0], [42.3, 49.0], [42.4, 49.0], [42.5, 49.0], [42.6, 49.0], [42.7, 49.0], [42.8, 49.0], [42.9, 49.0], [43.0, 49.0], [43.1, 49.0], [43.2, 49.0], [43.3, 49.0], [43.4, 49.0], [43.5, 49.0], [43.6, 49.0], [43.7, 49.0], [43.8, 49.0], [43.9, 49.0], [44.0, 49.0], [44.1, 49.0], [44.2, 49.0], [44.3, 49.0], [44.4, 49.0], [44.5, 49.0], [44.6, 49.0], [44.7, 49.0], [44.8, 49.0], [44.9, 49.0], [45.0, 49.0], [45.1, 49.0], [45.2, 49.0], [45.3, 49.0], [45.4, 49.0], [45.5, 49.0], [45.6, 49.0], [45.7, 49.0], [45.8, 49.0], [45.9, 49.0], [46.0, 49.0], [46.1, 49.0], [46.2, 49.0], [46.3, 49.0], [46.4, 50.0], [46.5, 50.0], [46.6, 50.0], [46.7, 50.0], [46.8, 50.0], [46.9, 50.0], [47.0, 50.0], [47.1, 50.0], [47.2, 50.0], [47.3, 50.0], [47.4, 50.0], [47.5, 50.0], [47.6, 50.0], [47.7, 50.0], [47.8, 50.0], [47.9, 50.0], [48.0, 50.0], [48.1, 50.0], [48.2, 50.0], [48.3, 50.0], [48.4, 50.0], [48.5, 50.0], [48.6, 50.0], [48.7, 50.0], [48.8, 50.0], [48.9, 50.0], [49.0, 50.0], [49.1, 50.0], [49.2, 50.0], [49.3, 50.0], [49.4, 50.0], [49.5, 50.0], [49.6, 50.0], [49.7, 50.0], [49.8, 50.0], [49.9, 50.0], [50.0, 50.0], [50.1, 50.0], [50.2, 50.0], [50.3, 50.0], [50.4, 50.0], [50.5, 50.0], [50.6, 50.0], [50.7, 50.0], [50.8, 50.0], [50.9, 50.0], [51.0, 50.0], [51.1, 50.0], [51.2, 50.0], [51.3, 50.0], [51.4, 50.0], [51.5, 50.0], [51.6, 50.0], [51.7, 50.0], [51.8, 50.0], [51.9, 50.0], [52.0, 50.0], [52.1, 50.0], [52.2, 50.0], [52.3, 50.0], [52.4, 50.0], [52.5, 50.0], [52.6, 50.0], [52.7, 50.0], [52.8, 50.0], [52.9, 50.0], [53.0, 50.0], [53.1, 50.0], [53.2, 50.0], [53.3, 50.0], [53.4, 50.0], [53.5, 50.0], [53.6, 50.0], [53.7, 50.0], [53.8, 50.0], [53.9, 50.0], [54.0, 50.0], [54.1, 50.0], [54.2, 50.0], [54.3, 50.0], [54.4, 50.0], [54.5, 50.0], [54.6, 50.0], [54.7, 50.0], [54.8, 50.0], [54.9, 50.0], [55.0, 50.0], [55.1, 50.0], [55.2, 50.0], [55.3, 50.0], [55.4, 50.0], [55.5, 50.0], [55.6, 50.0], [55.7, 50.0], [55.8, 50.0], [55.9, 50.0], [56.0, 50.0], [56.1, 50.0], [56.2, 50.0], [56.3, 50.0], [56.4, 50.0], [56.5, 50.0], [56.6, 50.0], [56.7, 50.0], [56.8, 50.0], [56.9, 50.0], [57.0, 50.0], [57.1, 50.0], [57.2, 50.0], [57.3, 50.0], [57.4, 50.0], [57.5, 50.0], [57.6, 50.0], [57.7, 50.0], [57.8, 50.0], [57.9, 50.0], [58.0, 50.0], [58.1, 50.0], [58.2, 50.0], [58.3, 50.0], [58.4, 50.0], [58.5, 50.0], [58.6, 50.0], [58.7, 50.0], [58.8, 50.0], [58.9, 50.0], [59.0, 50.0], [59.1, 50.0], [59.2, 50.0], [59.3, 50.0], [59.4, 50.0], [59.5, 50.0], [59.6, 50.0], [59.7, 51.0], [59.8, 51.0], [59.9, 51.0], [60.0, 51.0], [60.1, 51.0], [60.2, 51.0], [60.3, 51.0], [60.4, 51.0], [60.5, 51.0], [60.6, 51.0], [60.7, 51.0], [60.8, 51.0], [60.9, 51.0], [61.0, 51.0], [61.1, 51.0], [61.2, 51.0], [61.3, 51.0], [61.4, 51.0], [61.5, 51.0], [61.6, 51.0], [61.7, 51.0], [61.8, 51.0], [61.9, 51.0], [62.0, 51.0], [62.1, 51.0], [62.2, 51.0], [62.3, 51.0], [62.4, 51.0], [62.5, 51.0], [62.6, 51.0], [62.7, 51.0], [62.8, 51.0], [62.9, 51.0], [63.0, 51.0], [63.1, 51.0], [63.2, 51.0], [63.3, 51.0], [63.4, 51.0], [63.5, 51.0], [63.6, 51.0], [63.7, 51.0], [63.8, 51.0], [63.9, 51.0], [64.0, 51.0], [64.1, 51.0], [64.2, 51.0], [64.3, 51.0], [64.4, 51.0], [64.5, 51.0], [64.6, 51.0], [64.7, 51.0], [64.8, 51.0], [64.9, 51.0], [65.0, 51.0], [65.1, 51.0], [65.2, 51.0], [65.3, 51.0], [65.4, 51.0], [65.5, 51.0], [65.6, 51.0], [65.7, 51.0], [65.8, 51.0], [65.9, 51.0], [66.0, 51.0], [66.1, 51.0], [66.2, 51.0], [66.3, 51.0], [66.4, 51.0], [66.5, 51.0], [66.6, 51.0], [66.7, 51.0], [66.8, 51.0], [66.9, 51.0], [67.0, 51.0], [67.1, 51.0], [67.2, 51.0], [67.3, 51.0], [67.4, 51.0], [67.5, 51.0], [67.6, 51.0], [67.7, 51.0], [67.8, 51.0], [67.9, 51.0], [68.0, 51.0], [68.1, 51.0], [68.2, 51.0], [68.3, 51.0], [68.4, 51.0], [68.5, 51.0], [68.6, 51.0], [68.7, 51.0], [68.8, 51.0], [68.9, 51.0], [69.0, 51.0], [69.1, 51.0], [69.2, 51.0], [69.3, 51.0], [69.4, 51.0], [69.5, 51.0], [69.6, 51.0], [69.7, 51.0], [69.8, 51.0], [69.9, 51.0], [70.0, 51.0], [70.1, 51.0], [70.2, 51.0], [70.3, 51.0], [70.4, 51.0], [70.5, 51.0], [70.6, 51.0], [70.7, 51.0], [70.8, 51.0], [70.9, 51.0], [71.0, 51.0], [71.1, 51.0], [71.2, 51.0], [71.3, 51.0], [71.4, 51.0], [71.5, 52.0], [71.6, 52.0], [71.7, 52.0], [71.8, 52.0], [71.9, 52.0], [72.0, 52.0], [72.1, 52.0], [72.2, 52.0], [72.3, 52.0], [72.4, 52.0], [72.5, 52.0], [72.6, 52.0], [72.7, 52.0], [72.8, 52.0], [72.9, 52.0], [73.0, 52.0], [73.1, 52.0], [73.2, 52.0], [73.3, 52.0], [73.4, 52.0], [73.5, 52.0], [73.6, 52.0], [73.7, 52.0], [73.8, 52.0], [73.9, 52.0], [74.0, 52.0], [74.1, 52.0], [74.2, 52.0], [74.3, 52.0], [74.4, 52.0], [74.5, 52.0], [74.6, 52.0], [74.7, 52.0], [74.8, 52.0], [74.9, 53.0], [75.0, 53.0], [75.1, 53.0], [75.2, 53.0], [75.3, 53.0], [75.4, 53.0], [75.5, 53.0], [75.6, 53.0], [75.7, 53.0], [75.8, 53.0], [75.9, 53.0], [76.0, 53.0], [76.1, 53.0], [76.2, 53.0], [76.3, 53.0], [76.4, 54.0], [76.5, 54.0], [76.6, 54.0], [76.7, 54.0], [76.8, 54.0], [76.9, 54.0], [77.0, 54.0], [77.1, 54.0], [77.2, 54.0], [77.3, 54.0], [77.4, 54.0], [77.5, 54.0], [77.6, 54.0], [77.7, 54.0], [77.8, 54.0], [77.9, 55.0], [78.0, 55.0], [78.1, 55.0], [78.2, 55.0], [78.3, 55.0], [78.4, 55.0], [78.5, 55.0], [78.6, 55.0], [78.7, 55.0], [78.8, 55.0], [78.9, 62.0], [79.0, 62.0], [79.1, 62.0], [79.2, 62.0], [79.3, 62.0], [79.4, 62.0], [79.5, 62.0], [79.6, 62.0], [79.7, 62.0], [79.8, 62.0], [79.9, 63.0], [80.0, 63.0], [80.1, 63.0], [80.2, 63.0], [80.3, 66.0], [80.4, 66.0], [80.5, 66.0], [80.6, 66.0], [80.7, 66.0], [80.8, 68.0], [80.9, 68.0], [81.0, 68.0], [81.1, 68.0], [81.2, 68.0], [81.3, 69.0], [81.4, 69.0], [81.5, 69.0], [81.6, 69.0], [81.7, 69.0], [81.8, 69.0], [81.9, 69.0], [82.0, 69.0], [82.1, 69.0], [82.2, 69.0], [82.3, 70.0], [82.4, 70.0], [82.5, 70.0], [82.6, 70.0], [82.7, 70.0], [82.8, 70.0], [82.9, 70.0], [83.0, 70.0], [83.1, 70.0], [83.2, 70.0], [83.3, 71.0], [83.4, 71.0], [83.5, 71.0], [83.6, 71.0], [83.7, 71.0], [83.8, 73.0], [83.9, 73.0], [84.0, 73.0], [84.1, 73.0], [84.2, 73.0], [84.3, 78.0], [84.4, 78.0], [84.5, 78.0], [84.6, 78.0], [84.7, 78.0], [84.8, 79.0], [84.9, 79.0], [85.0, 79.0], [85.1, 79.0], [85.2, 79.0], [85.3, 81.0], [85.4, 81.0], [85.5, 81.0], [85.6, 81.0], [85.7, 81.0], [85.8, 84.0], [85.9, 84.0], [86.0, 84.0], [86.1, 84.0], [86.2, 84.0], [86.3, 85.0], [86.4, 85.0], [86.5, 85.0], [86.6, 85.0], [86.7, 86.0], [86.8, 86.0], [86.9, 86.0], [87.0, 86.0], [87.1, 86.0], [87.2, 87.0], [87.3, 87.0], [87.4, 87.0], [87.5, 87.0], [87.6, 87.0], [87.7, 87.0], [87.8, 87.0], [87.9, 87.0], [88.0, 87.0], [88.1, 87.0], [88.2, 88.0], [88.3, 88.0], [88.4, 88.0], [88.5, 88.0], [88.6, 88.0], [88.7, 88.0], [88.8, 88.0], [88.9, 88.0], [89.0, 88.0], [89.1, 88.0], [89.2, 93.0], [89.3, 93.0], [89.4, 93.0], [89.5, 93.0], [89.6, 93.0], [89.7, 94.0], [89.8, 94.0], [89.9, 94.0], [90.0, 94.0], [90.1, 94.0], [90.2, 96.0], [90.3, 96.0], [90.4, 96.0], [90.5, 96.0], [90.6, 96.0], [90.7, 97.0], [90.8, 97.0], [90.9, 97.0], [91.0, 97.0], [91.1, 97.0], [91.2, 97.0], [91.3, 97.0], [91.4, 97.0], [91.5, 97.0], [91.6, 97.0], [91.7, 101.0], [91.8, 101.0], [91.9, 101.0], [92.0, 101.0], [92.1, 101.0], [92.2, 102.0], [92.3, 102.0], [92.4, 102.0], [92.5, 102.0], [92.6, 102.0], [92.7, 103.0], [92.8, 103.0], [92.9, 103.0], [93.0, 103.0], [93.1, 103.0], [93.2, 104.0], [93.3, 104.0], [93.4, 104.0], [93.5, 104.0], [93.6, 110.0], [93.7, 110.0], [93.8, 110.0], [93.9, 110.0], [94.0, 110.0], [94.1, 112.0], [94.2, 112.0], [94.3, 112.0], [94.4, 112.0], [94.5, 112.0], [94.6, 112.0], [94.7, 112.0], [94.8, 112.0], [94.9, 112.0], [95.0, 112.0], [95.1, 119.0], [95.2, 119.0], [95.3, 119.0], [95.4, 119.0], [95.5, 119.0], [95.6, 124.0], [95.7, 124.0], [95.8, 124.0], [95.9, 124.0], [96.0, 124.0], [96.1, 127.0], [96.2, 127.0], [96.3, 127.0], [96.4, 127.0], [96.5, 127.0], [96.6, 127.0], [96.7, 127.0], [96.8, 127.0], [96.9, 127.0], [97.0, 127.0], [97.1, 128.0], [97.2, 128.0], [97.3, 128.0], [97.4, 128.0], [97.5, 128.0], [97.6, 130.0], [97.7, 130.0], [97.8, 130.0], [97.9, 130.0], [98.0, 130.0], [98.1, 147.0], [98.2, 147.0], [98.3, 147.0], [98.4, 147.0], [98.5, 147.0], [98.6, 149.0], [98.7, 149.0], [98.8, 149.0], [98.9, 149.0], [99.0, 149.0], [99.1, 155.0], [99.2, 155.0], [99.3, 155.0], [99.4, 155.0], [99.5, 155.0], [99.6, 157.0], [99.7, 157.0], [99.8, 157.0], [99.9, 157.0]], "isOverall": false, "label": "Health Check", "isController": false}, {"data": [[0.0, 57.0], [0.1, 57.0], [0.2, 57.0], [0.3, 57.0], [0.4, 57.0], [0.5, 57.0], [0.6, 57.0], [0.7, 57.0], [0.8, 57.0], [0.9, 57.0], [1.0, 58.0], [1.1, 58.0], [1.2, 58.0], [1.3, 58.0], [1.4, 58.0], [1.5, 58.0], [1.6, 58.0], [1.7, 58.0], [1.8, 58.0], [1.9, 58.0], [2.0, 58.0], [2.1, 58.0], [2.2, 58.0], [2.3, 59.0], [2.4, 59.0], [2.5, 59.0], [2.6, 59.0], [2.7, 59.0], [2.8, 59.0], [2.9, 59.0], [3.0, 59.0], [3.1, 59.0], [3.2, 59.0], [3.3, 59.0], [3.4, 59.0], [3.5, 59.0], [3.6, 59.0], [3.7, 59.0], [3.8, 59.0], [3.9, 59.0], [4.0, 60.0], [4.1, 60.0], [4.2, 60.0], [4.3, 60.0], [4.4, 60.0], [4.5, 60.0], [4.6, 60.0], [4.7, 60.0], [4.8, 60.0], [4.9, 60.0], [5.0, 60.0], [5.1, 60.0], [5.2, 60.0], [5.3, 60.0], [5.4, 60.0], [5.5, 60.0], [5.6, 60.0], [5.7, 60.0], [5.8, 60.0], [5.9, 60.0], [6.0, 60.0], [6.1, 60.0], [6.2, 60.0], [6.3, 60.0], [6.4, 60.0], [6.5, 60.0], [6.6, 60.0], [6.7, 60.0], [6.8, 61.0], [6.9, 61.0], [7.0, 61.0], [7.1, 61.0], [7.2, 61.0], [7.3, 61.0], [7.4, 61.0], [7.5, 61.0], [7.6, 61.0], [7.7, 61.0], [7.8, 61.0], [7.9, 61.0], [8.0, 61.0], [8.1, 61.0], [8.2, 61.0], [8.3, 61.0], [8.4, 61.0], [8.5, 61.0], [8.6, 61.0], [8.7, 61.0], [8.8, 61.0], [8.9, 61.0], [9.0, 61.0], [9.1, 61.0], [9.2, 61.0], [9.3, 61.0], [9.4, 61.0], [9.5, 61.0], [9.6, 61.0], [9.7, 61.0], [9.8, 61.0], [9.9, 62.0], [10.0, 62.0], [10.1, 62.0], [10.2, 62.0], [10.3, 62.0], [10.4, 62.0], [10.5, 62.0], [10.6, 62.0], [10.7, 62.0], [10.8, 62.0], [10.9, 62.0], [11.0, 62.0], [11.1, 62.0], [11.2, 62.0], [11.3, 62.0], [11.4, 62.0], [11.5, 62.0], [11.6, 62.0], [11.7, 62.0], [11.8, 62.0], [11.9, 62.0], [12.0, 62.0], [12.1, 62.0], [12.2, 62.0], [12.3, 62.0], [12.4, 62.0], [12.5, 62.0], [12.6, 62.0], [12.7, 62.0], [12.8, 62.0], [12.9, 62.0], [13.0, 62.0], [13.1, 62.0], [13.2, 62.0], [13.3, 62.0], [13.4, 62.0], [13.5, 62.0], [13.6, 62.0], [13.7, 63.0], [13.8, 63.0], [13.9, 63.0], [14.0, 63.0], [14.1, 63.0], [14.2, 63.0], [14.3, 63.0], [14.4, 63.0], [14.5, 63.0], [14.6, 63.0], [14.7, 63.0], [14.8, 63.0], [14.9, 63.0], [15.0, 63.0], [15.1, 63.0], [15.2, 63.0], [15.3, 63.0], [15.4, 63.0], [15.5, 63.0], [15.6, 63.0], [15.7, 63.0], [15.8, 63.0], [15.9, 63.0], [16.0, 63.0], [16.1, 63.0], [16.2, 64.0], [16.3, 64.0], [16.4, 64.0], [16.5, 64.0], [16.6, 64.0], [16.7, 64.0], [16.8, 64.0], [16.9, 64.0], [17.0, 64.0], [17.1, 64.0], [17.2, 64.0], [17.3, 64.0], [17.4, 64.0], [17.5, 64.0], [17.6, 64.0], [17.7, 64.0], [17.8, 64.0], [17.9, 64.0], [18.0, 64.0], [18.1, 64.0], [18.2, 64.0], [18.3, 64.0], [18.4, 64.0], [18.5, 64.0], [18.6, 64.0], [18.7, 64.0], [18.8, 65.0], [18.9, 65.0], [19.0, 65.0], [19.1, 65.0], [19.2, 65.0], [19.3, 65.0], [19.4, 65.0], [19.5, 65.0], [19.6, 65.0], [19.7, 65.0], [19.8, 65.0], [19.9, 65.0], [20.0, 65.0], [20.1, 65.0], [20.2, 65.0], [20.3, 65.0], [20.4, 65.0], [20.5, 65.0], [20.6, 65.0], [20.7, 65.0], [20.8, 65.0], [20.9, 66.0], [21.0, 66.0], [21.1, 66.0], [21.2, 66.0], [21.3, 66.0], [21.4, 66.0], [21.5, 66.0], [21.6, 66.0], [21.7, 66.0], [21.8, 66.0], [21.9, 66.0], [22.0, 66.0], [22.1, 66.0], [22.2, 66.0], [22.3, 66.0], [22.4, 67.0], [22.5, 67.0], [22.6, 67.0], [22.7, 67.0], [22.8, 67.0], [22.9, 67.0], [23.0, 67.0], [23.1, 67.0], [23.2, 67.0], [23.3, 67.0], [23.4, 67.0], [23.5, 67.0], [23.6, 68.0], [23.7, 68.0], [23.8, 68.0], [23.9, 68.0], [24.0, 68.0], [24.1, 68.0], [24.2, 68.0], [24.3, 68.0], [24.4, 68.0], [24.5, 68.0], [24.6, 68.0], [24.7, 69.0], [24.8, 69.0], [24.9, 69.0], [25.0, 69.0], [25.1, 69.0], [25.2, 69.0], [25.3, 69.0], [25.4, 69.0], [25.5, 69.0], [25.6, 69.0], [25.7, 69.0], [25.8, 69.0], [25.9, 69.0], [26.0, 69.0], [26.1, 69.0], [26.2, 69.0], [26.3, 69.0], [26.4, 69.0], [26.5, 69.0], [26.6, 69.0], [26.7, 69.0], [26.8, 69.0], [26.9, 69.0], [27.0, 70.0], [27.1, 70.0], [27.2, 70.0], [27.3, 70.0], [27.4, 70.0], [27.5, 70.0], [27.6, 70.0], [27.7, 70.0], [27.8, 70.0], [27.9, 70.0], [28.0, 70.0], [28.1, 70.0], [28.2, 70.0], [28.3, 70.0], [28.4, 70.0], [28.5, 70.0], [28.6, 71.0], [28.7, 71.0], [28.8, 71.0], [28.9, 71.0], [29.0, 71.0], [29.1, 71.0], [29.2, 71.0], [29.3, 71.0], [29.4, 71.0], [29.5, 71.0], [29.6, 71.0], [29.7, 71.0], [29.8, 71.0], [29.9, 72.0], [30.0, 72.0], [30.1, 72.0], [30.2, 72.0], [30.3, 72.0], [30.4, 72.0], [30.5, 72.0], [30.6, 72.0], [30.7, 72.0], [30.8, 72.0], [30.9, 72.0], [31.0, 72.0], [31.1, 72.0], [31.2, 72.0], [31.3, 72.0], [31.4, 72.0], [31.5, 73.0], [31.6, 73.0], [31.7, 73.0], [31.8, 73.0], [31.9, 73.0], [32.0, 73.0], [32.1, 73.0], [32.2, 73.0], [32.3, 73.0], [32.4, 73.0], [32.5, 73.0], [32.6, 73.0], [32.7, 73.0], [32.8, 73.0], [32.9, 73.0], [33.0, 73.0], [33.1, 74.0], [33.2, 74.0], [33.3, 74.0], [33.4, 74.0], [33.5, 74.0], [33.6, 74.0], [33.7, 74.0], [33.8, 75.0], [33.9, 75.0], [34.0, 75.0], [34.1, 75.0], [34.2, 75.0], [34.3, 75.0], [34.4, 75.0], [34.5, 75.0], [34.6, 76.0], [34.7, 76.0], [34.8, 76.0], [34.9, 76.0], [35.0, 76.0], [35.1, 76.0], [35.2, 77.0], [35.3, 77.0], [35.4, 77.0], [35.5, 77.0], [35.6, 77.0], [35.7, 78.0], [35.8, 78.0], [35.9, 78.0], [36.0, 78.0], [36.1, 78.0], [36.2, 78.0], [36.3, 78.0], [36.4, 79.0], [36.5, 79.0], [36.6, 79.0], [36.7, 79.0], [36.8, 80.0], [36.9, 80.0], [37.0, 80.0], [37.1, 80.0], [37.2, 80.0], [37.3, 80.0], [37.4, 80.0], [37.5, 81.0], [37.6, 81.0], [37.7, 81.0], [37.8, 81.0], [37.9, 81.0], [38.0, 81.0], [38.1, 82.0], [38.2, 82.0], [38.3, 82.0], [38.4, 82.0], [38.5, 82.0], [38.6, 82.0], [38.7, 82.0], [38.8, 82.0], [38.9, 83.0], [39.0, 83.0], [39.1, 83.0], [39.2, 83.0], [39.3, 83.0], [39.4, 84.0], [39.5, 84.0], [39.6, 84.0], [39.7, 84.0], [39.8, 84.0], [39.9, 84.0], [40.0, 84.0], [40.1, 84.0], [40.2, 84.0], [40.3, 84.0], [40.4, 85.0], [40.5, 85.0], [40.6, 85.0], [40.7, 85.0], [40.8, 85.0], [40.9, 85.0], [41.0, 85.0], [41.1, 85.0], [41.2, 86.0], [41.3, 86.0], [41.4, 86.0], [41.5, 86.0], [41.6, 86.0], [41.7, 86.0], [41.8, 87.0], [41.9, 87.0], [42.0, 87.0], [42.1, 87.0], [42.2, 87.0], [42.3, 87.0], [42.4, 87.0], [42.5, 87.0], [42.6, 87.0], [42.7, 87.0], [42.8, 88.0], [42.9, 88.0], [43.0, 88.0], [43.1, 88.0], [43.2, 88.0], [43.3, 88.0], [43.4, 88.0], [43.5, 88.0], [43.6, 89.0], [43.7, 89.0], [43.8, 89.0], [43.9, 89.0], [44.0, 89.0], [44.1, 89.0], [44.2, 90.0], [44.3, 90.0], [44.4, 90.0], [44.5, 91.0], [44.6, 91.0], [44.7, 91.0], [44.8, 91.0], [44.9, 91.0], [45.0, 91.0], [45.1, 91.0], [45.2, 91.0], [45.3, 91.0], [45.4, 92.0], [45.5, 92.0], [45.6, 92.0], [45.7, 92.0], [45.8, 93.0], [45.9, 93.0], [46.0, 93.0], [46.1, 93.0], [46.2, 93.0], [46.3, 93.0], [46.4, 94.0], [46.5, 94.0], [46.6, 94.0], [46.7, 94.0], [46.8, 94.0], [46.9, 95.0], [47.0, 95.0], [47.1, 95.0], [47.2, 95.0], [47.3, 95.0], [47.4, 95.0], [47.5, 96.0], [47.6, 96.0], [47.7, 96.0], [47.8, 96.0], [47.9, 96.0], [48.0, 96.0], [48.1, 96.0], [48.2, 96.0], [48.3, 96.0], [48.4, 97.0], [48.5, 98.0], [48.6, 99.0], [48.7, 99.0], [48.8, 99.0], [48.9, 100.0], [49.0, 100.0], [49.1, 100.0], [49.2, 100.0], [49.3, 100.0], [49.4, 100.0], [49.5, 101.0], [49.6, 101.0], [49.7, 101.0], [49.8, 101.0], [49.9, 102.0], [50.0, 102.0], [50.1, 102.0], [50.2, 103.0], [50.3, 103.0], [50.4, 103.0], [50.5, 104.0], [50.6, 104.0], [50.7, 104.0], [50.8, 105.0], [50.9, 106.0], [51.0, 106.0], [51.1, 106.0], [51.2, 106.0], [51.3, 107.0], [51.4, 108.0], [51.5, 108.0], [51.6, 108.0], [51.7, 108.0], [51.8, 108.0], [51.9, 109.0], [52.0, 109.0], [52.1, 110.0], [52.2, 110.0], [52.3, 110.0], [52.4, 110.0], [52.5, 110.0], [52.6, 111.0], [52.7, 111.0], [52.8, 112.0], [52.9, 112.0], [53.0, 112.0], [53.1, 113.0], [53.2, 114.0], [53.3, 114.0], [53.4, 114.0], [53.5, 114.0], [53.6, 115.0], [53.7, 115.0], [53.8, 115.0], [53.9, 116.0], [54.0, 116.0], [54.1, 116.0], [54.2, 117.0], [54.3, 117.0], [54.4, 118.0], [54.5, 118.0], [54.6, 119.0], [54.7, 119.0], [54.8, 119.0], [54.9, 120.0], [55.0, 120.0], [55.1, 120.0], [55.2, 120.0], [55.3, 120.0], [55.4, 120.0], [55.5, 121.0], [55.6, 121.0], [55.7, 122.0], [55.8, 122.0], [55.9, 123.0], [56.0, 123.0], [56.1, 123.0], [56.2, 123.0], [56.3, 123.0], [56.4, 124.0], [56.5, 124.0], [56.6, 124.0], [56.7, 124.0], [56.8, 124.0], [56.9, 124.0], [57.0, 126.0], [57.1, 127.0], [57.2, 127.0], [57.3, 127.0], [57.4, 128.0], [57.5, 128.0], [57.6, 128.0], [57.7, 129.0], [57.8, 129.0], [57.9, 130.0], [58.0, 131.0], [58.1, 131.0], [58.2, 131.0], [58.3, 131.0], [58.4, 131.0], [58.5, 131.0], [58.6, 132.0], [58.7, 132.0], [58.8, 132.0], [58.9, 132.0], [59.0, 133.0], [59.1, 133.0], [59.2, 133.0], [59.3, 133.0], [59.4, 133.0], [59.5, 135.0], [59.6, 135.0], [59.7, 136.0], [59.8, 136.0], [59.9, 137.0], [60.0, 138.0], [60.1, 139.0], [60.2, 139.0], [60.3, 139.0], [60.4, 139.0], [60.5, 140.0], [60.6, 141.0], [60.7, 142.0], [60.8, 144.0], [60.9, 144.0], [61.0, 144.0], [61.1, 145.0], [61.2, 145.0], [61.3, 145.0], [61.4, 145.0], [61.5, 146.0], [61.6, 146.0], [61.7, 146.0], [61.8, 147.0], [61.9, 147.0], [62.0, 147.0], [62.1, 147.0], [62.2, 148.0], [62.3, 148.0], [62.4, 148.0], [62.5, 149.0], [62.6, 150.0], [62.7, 151.0], [62.8, 151.0], [62.9, 151.0], [63.0, 152.0], [63.1, 152.0], [63.2, 152.0], [63.3, 152.0], [63.4, 152.0], [63.5, 153.0], [63.6, 153.0], [63.7, 153.0], [63.8, 153.0], [63.9, 154.0], [64.0, 154.0], [64.1, 154.0], [64.2, 155.0], [64.3, 156.0], [64.4, 156.0], [64.5, 157.0], [64.6, 158.0], [64.7, 158.0], [64.8, 158.0], [64.9, 159.0], [65.0, 159.0], [65.1, 160.0], [65.2, 161.0], [65.3, 161.0], [65.4, 161.0], [65.5, 162.0], [65.6, 162.0], [65.7, 162.0], [65.8, 163.0], [65.9, 164.0], [66.0, 165.0], [66.1, 165.0], [66.2, 165.0], [66.3, 165.0], [66.4, 166.0], [66.5, 166.0], [66.6, 166.0], [66.7, 167.0], [66.8, 167.0], [66.9, 167.0], [67.0, 167.0], [67.1, 170.0], [67.2, 171.0], [67.3, 171.0], [67.4, 172.0], [67.5, 173.0], [67.6, 173.0], [67.7, 174.0], [67.8, 175.0], [67.9, 175.0], [68.0, 176.0], [68.1, 176.0], [68.2, 176.0], [68.3, 177.0], [68.4, 180.0], [68.5, 181.0], [68.6, 182.0], [68.7, 183.0], [68.8, 184.0], [68.9, 185.0], [69.0, 185.0], [69.1, 185.0], [69.2, 186.0], [69.3, 188.0], [69.4, 189.0], [69.5, 191.0], [69.6, 191.0], [69.7, 192.0], [69.8, 193.0], [69.9, 194.0], [70.0, 194.0], [70.1, 195.0], [70.2, 196.0], [70.3, 196.0], [70.4, 197.0], [70.5, 197.0], [70.6, 198.0], [70.7, 198.0], [70.8, 199.0], [70.9, 200.0], [71.0, 200.0], [71.1, 200.0], [71.2, 201.0], [71.3, 201.0], [71.4, 201.0], [71.5, 204.0], [71.6, 204.0], [71.7, 205.0], [71.8, 207.0], [71.9, 209.0], [72.0, 209.0], [72.1, 210.0], [72.2, 212.0], [72.3, 212.0], [72.4, 213.0], [72.5, 213.0], [72.6, 215.0], [72.7, 216.0], [72.8, 216.0], [72.9, 218.0], [73.0, 218.0], [73.1, 219.0], [73.2, 223.0], [73.3, 223.0], [73.4, 226.0], [73.5, 226.0], [73.6, 229.0], [73.7, 234.0], [73.8, 234.0], [73.9, 234.0], [74.0, 235.0], [74.1, 236.0], [74.2, 236.0], [74.3, 237.0], [74.4, 237.0], [74.5, 239.0], [74.6, 240.0], [74.7, 240.0], [74.8, 242.0], [74.9, 244.0], [75.0, 245.0], [75.1, 245.0], [75.2, 246.0], [75.3, 246.0], [75.4, 251.0], [75.5, 251.0], [75.6, 252.0], [75.7, 253.0], [75.8, 254.0], [75.9, 254.0], [76.0, 255.0], [76.1, 256.0], [76.2, 257.0], [76.3, 258.0], [76.4, 259.0], [76.5, 259.0], [76.6, 259.0], [76.7, 260.0], [76.8, 260.0], [76.9, 260.0], [77.0, 264.0], [77.1, 264.0], [77.2, 265.0], [77.3, 266.0], [77.4, 268.0], [77.5, 272.0], [77.6, 272.0], [77.7, 273.0], [77.8, 273.0], [77.9, 273.0], [78.0, 273.0], [78.1, 273.0], [78.2, 274.0], [78.3, 274.0], [78.4, 279.0], [78.5, 280.0], [78.6, 282.0], [78.7, 283.0], [78.8, 283.0], [78.9, 285.0], [79.0, 286.0], [79.1, 287.0], [79.2, 289.0], [79.3, 291.0], [79.4, 291.0], [79.5, 292.0], [79.6, 293.0], [79.7, 293.0], [79.8, 294.0], [79.9, 298.0], [80.0, 299.0], [80.1, 299.0], [80.2, 301.0], [80.3, 304.0], [80.4, 304.0], [80.5, 305.0], [80.6, 306.0], [80.7, 306.0], [80.8, 308.0], [80.9, 309.0], [81.0, 310.0], [81.1, 310.0], [81.2, 312.0], [81.3, 313.0], [81.4, 314.0], [81.5, 317.0], [81.6, 317.0], [81.7, 320.0], [81.8, 321.0], [81.9, 328.0], [82.0, 332.0], [82.1, 333.0], [82.2, 336.0], [82.3, 336.0], [82.4, 337.0], [82.5, 339.0], [82.6, 339.0], [82.7, 339.0], [82.8, 339.0], [82.9, 343.0], [83.0, 345.0], [83.1, 346.0], [83.2, 348.0], [83.3, 348.0], [83.4, 351.0], [83.5, 351.0], [83.6, 352.0], [83.7, 353.0], [83.8, 355.0], [83.9, 357.0], [84.0, 359.0], [84.1, 360.0], [84.2, 361.0], [84.3, 362.0], [84.4, 368.0], [84.5, 368.0], [84.6, 368.0], [84.7, 369.0], [84.8, 372.0], [84.9, 372.0], [85.0, 376.0], [85.1, 377.0], [85.2, 378.0], [85.3, 383.0], [85.4, 384.0], [85.5, 386.0], [85.6, 386.0], [85.7, 387.0], [85.8, 388.0], [85.9, 393.0], [86.0, 398.0], [86.1, 401.0], [86.2, 401.0], [86.3, 401.0], [86.4, 401.0], [86.5, 406.0], [86.6, 409.0], [86.7, 411.0], [86.8, 411.0], [86.9, 412.0], [87.0, 413.0], [87.1, 413.0], [87.2, 414.0], [87.3, 418.0], [87.4, 418.0], [87.5, 423.0], [87.6, 423.0], [87.7, 425.0], [87.8, 426.0], [87.9, 427.0], [88.0, 427.0], [88.1, 427.0], [88.2, 434.0], [88.3, 434.0], [88.4, 436.0], [88.5, 437.0], [88.6, 437.0], [88.7, 438.0], [88.8, 438.0], [88.9, 439.0], [89.0, 441.0], [89.1, 444.0], [89.2, 445.0], [89.3, 451.0], [89.4, 454.0], [89.5, 455.0], [89.6, 461.0], [89.7, 461.0], [89.8, 467.0], [89.9, 470.0], [90.0, 470.0], [90.1, 471.0], [90.2, 476.0], [90.3, 483.0], [90.4, 485.0], [90.5, 485.0], [90.6, 487.0], [90.7, 491.0], [90.8, 491.0], [90.9, 491.0], [91.0, 492.0], [91.1, 492.0], [91.2, 499.0], [91.3, 504.0], [91.4, 504.0], [91.5, 506.0], [91.6, 509.0], [91.7, 514.0], [91.8, 530.0], [91.9, 530.0], [92.0, 532.0], [92.1, 538.0], [92.2, 555.0], [92.3, 560.0], [92.4, 567.0], [92.5, 569.0], [92.6, 573.0], [92.7, 583.0], [92.8, 585.0], [92.9, 586.0], [93.0, 586.0], [93.1, 586.0], [93.2, 586.0], [93.3, 588.0], [93.4, 590.0], [93.5, 598.0], [93.6, 611.0], [93.7, 619.0], [93.8, 621.0], [93.9, 622.0], [94.0, 623.0], [94.1, 623.0], [94.2, 625.0], [94.3, 625.0], [94.4, 627.0], [94.5, 632.0], [94.6, 632.0], [94.7, 639.0], [94.8, 642.0], [94.9, 642.0], [95.0, 643.0], [95.1, 643.0], [95.2, 643.0], [95.3, 645.0], [95.4, 646.0], [95.5, 649.0], [95.6, 649.0], [95.7, 665.0], [95.8, 667.0], [95.9, 670.0], [96.0, 678.0], [96.1, 678.0], [96.2, 685.0], [96.3, 695.0], [96.4, 702.0], [96.5, 703.0], [96.6, 703.0], [96.7, 708.0], [96.8, 713.0], [96.9, 714.0], [97.0, 733.0], [97.1, 733.0], [97.2, 750.0], [97.3, 751.0], [97.4, 783.0], [97.5, 811.0], [97.6, 812.0], [97.7, 832.0], [97.8, 843.0], [97.9, 852.0], [98.0, 855.0], [98.1, 858.0], [98.2, 861.0], [98.3, 861.0], [98.4, 889.0], [98.5, 895.0], [98.6, 930.0], [98.7, 933.0], [98.8, 940.0], [98.9, 942.0], [99.0, 963.0], [99.1, 974.0], [99.2, 974.0], [99.3, 987.0], [99.4, 1003.0], [99.5, 1059.0], [99.6, 1076.0], [99.7, 1078.0], [99.8, 1097.0], [99.9, 1106.0], [100.0, 1106.0]], "isOverall": false, "label": "Tâches", "isController": false}, {"data": [[0.0, 57.0], [0.1, 57.0], [0.2, 58.0], [0.3, 59.0], [0.4, 59.0], [0.5, 59.0], [0.6, 59.0], [0.7, 59.0], [0.8, 59.0], [0.9, 59.0], [1.0, 59.0], [1.1, 59.0], [1.2, 59.0], [1.3, 59.0], [1.4, 60.0], [1.5, 60.0], [1.6, 60.0], [1.7, 60.0], [1.8, 60.0], [1.9, 60.0], [2.0, 60.0], [2.1, 60.0], [2.2, 61.0], [2.3, 61.0], [2.4, 61.0], [2.5, 61.0], [2.6, 61.0], [2.7, 61.0], [2.8, 61.0], [2.9, 61.0], [3.0, 61.0], [3.1, 61.0], [3.2, 61.0], [3.3, 61.0], [3.4, 61.0], [3.5, 61.0], [3.6, 61.0], [3.7, 62.0], [3.8, 62.0], [3.9, 62.0], [4.0, 62.0], [4.1, 62.0], [4.2, 62.0], [4.3, 62.0], [4.4, 62.0], [4.5, 62.0], [4.6, 62.0], [4.7, 62.0], [4.8, 62.0], [4.9, 62.0], [5.0, 62.0], [5.1, 62.0], [5.2, 62.0], [5.3, 62.0], [5.4, 62.0], [5.5, 62.0], [5.6, 62.0], [5.7, 62.0], [5.8, 63.0], [5.9, 63.0], [6.0, 63.0], [6.1, 63.0], [6.2, 63.0], [6.3, 63.0], [6.4, 63.0], [6.5, 63.0], [6.6, 63.0], [6.7, 64.0], [6.8, 64.0], [6.9, 64.0], [7.0, 64.0], [7.1, 64.0], [7.2, 64.0], [7.3, 64.0], [7.4, 64.0], [7.5, 64.0], [7.6, 64.0], [7.7, 64.0], [7.8, 64.0], [7.9, 64.0], [8.0, 64.0], [8.1, 64.0], [8.2, 64.0], [8.3, 64.0], [8.4, 64.0], [8.5, 64.0], [8.6, 65.0], [8.7, 65.0], [8.8, 65.0], [8.9, 65.0], [9.0, 65.0], [9.1, 65.0], [9.2, 65.0], [9.3, 65.0], [9.4, 65.0], [9.5, 65.0], [9.6, 65.0], [9.7, 65.0], [9.8, 65.0], [9.9, 65.0], [10.0, 65.0], [10.1, 65.0], [10.2, 65.0], [10.3, 65.0], [10.4, 65.0], [10.5, 65.0], [10.6, 65.0], [10.7, 65.0], [10.8, 66.0], [10.9, 66.0], [11.0, 66.0], [11.1, 66.0], [11.2, 66.0], [11.3, 66.0], [11.4, 66.0], [11.5, 66.0], [11.6, 66.0], [11.7, 66.0], [11.8, 67.0], [11.9, 67.0], [12.0, 67.0], [12.1, 67.0], [12.2, 67.0], [12.3, 67.0], [12.4, 67.0], [12.5, 67.0], [12.6, 67.0], [12.7, 67.0], [12.8, 67.0], [12.9, 67.0], [13.0, 67.0], [13.1, 67.0], [13.2, 67.0], [13.3, 67.0], [13.4, 68.0], [13.5, 68.0], [13.6, 68.0], [13.7, 68.0], [13.8, 68.0], [13.9, 68.0], [14.0, 68.0], [14.1, 68.0], [14.2, 68.0], [14.3, 68.0], [14.4, 68.0], [14.5, 68.0], [14.6, 68.0], [14.7, 68.0], [14.8, 69.0], [14.9, 69.0], [15.0, 69.0], [15.1, 69.0], [15.2, 69.0], [15.3, 69.0], [15.4, 69.0], [15.5, 69.0], [15.6, 69.0], [15.7, 69.0], [15.8, 69.0], [15.9, 69.0], [16.0, 69.0], [16.1, 70.0], [16.2, 70.0], [16.3, 70.0], [16.4, 70.0], [16.5, 70.0], [16.6, 70.0], [16.7, 70.0], [16.8, 71.0], [16.9, 71.0], [17.0, 71.0], [17.1, 71.0], [17.2, 71.0], [17.3, 71.0], [17.4, 71.0], [17.5, 71.0], [17.6, 71.0], [17.7, 71.0], [17.8, 72.0], [17.9, 72.0], [18.0, 72.0], [18.1, 72.0], [18.2, 72.0], [18.3, 72.0], [18.4, 72.0], [18.5, 72.0], [18.6, 72.0], [18.7, 72.0], [18.8, 73.0], [18.9, 73.0], [19.0, 73.0], [19.1, 73.0], [19.2, 73.0], [19.3, 73.0], [19.4, 73.0], [19.5, 73.0], [19.6, 73.0], [19.7, 73.0], [19.8, 73.0], [19.9, 74.0], [20.0, 74.0], [20.1, 74.0], [20.2, 74.0], [20.3, 74.0], [20.4, 75.0], [20.5, 75.0], [20.6, 75.0], [20.7, 75.0], [20.8, 75.0], [20.9, 75.0], [21.0, 75.0], [21.1, 76.0], [21.2, 76.0], [21.3, 76.0], [21.4, 76.0], [21.5, 76.0], [21.6, 76.0], [21.7, 76.0], [21.8, 77.0], [21.9, 77.0], [22.0, 77.0], [22.1, 77.0], [22.2, 77.0], [22.3, 78.0], [22.4, 78.0], [22.5, 78.0], [22.6, 78.0], [22.7, 79.0], [22.8, 79.0], [22.9, 79.0], [23.0, 79.0], [23.1, 79.0], [23.2, 79.0], [23.3, 79.0], [23.4, 80.0], [23.5, 80.0], [23.6, 80.0], [23.7, 80.0], [23.8, 80.0], [23.9, 80.0], [24.0, 80.0], [24.1, 80.0], [24.2, 80.0], [24.3, 80.0], [24.4, 81.0], [24.5, 81.0], [24.6, 81.0], [24.7, 81.0], [24.8, 82.0], [24.9, 82.0], [25.0, 82.0], [25.1, 82.0], [25.2, 82.0], [25.3, 82.0], [25.4, 82.0], [25.5, 82.0], [25.6, 83.0], [25.7, 83.0], [25.8, 83.0], [25.9, 83.0], [26.0, 83.0], [26.1, 83.0], [26.2, 83.0], [26.3, 83.0], [26.4, 83.0], [26.5, 84.0], [26.6, 84.0], [26.7, 84.0], [26.8, 84.0], [26.9, 84.0], [27.0, 85.0], [27.1, 85.0], [27.2, 85.0], [27.3, 85.0], [27.4, 85.0], [27.5, 85.0], [27.6, 85.0], [27.7, 85.0], [27.8, 86.0], [27.9, 86.0], [28.0, 86.0], [28.1, 86.0], [28.2, 86.0], [28.3, 86.0], [28.4, 86.0], [28.5, 86.0], [28.6, 87.0], [28.7, 87.0], [28.8, 87.0], [28.9, 87.0], [29.0, 87.0], [29.1, 87.0], [29.2, 88.0], [29.3, 88.0], [29.4, 88.0], [29.5, 88.0], [29.6, 88.0], [29.7, 88.0], [29.8, 88.0], [29.9, 88.0], [30.0, 88.0], [30.1, 88.0], [30.2, 89.0], [30.3, 89.0], [30.4, 89.0], [30.5, 89.0], [30.6, 89.0], [30.7, 89.0], [30.8, 89.0], [30.9, 89.0], [31.0, 90.0], [31.1, 90.0], [31.2, 90.0], [31.3, 90.0], [31.4, 90.0], [31.5, 90.0], [31.6, 90.0], [31.7, 90.0], [31.8, 90.0], [31.9, 90.0], [32.0, 90.0], [32.1, 90.0], [32.2, 91.0], [32.3, 91.0], [32.4, 91.0], [32.5, 91.0], [32.6, 91.0], [32.7, 91.0], [32.8, 91.0], [32.9, 92.0], [33.0, 92.0], [33.1, 92.0], [33.2, 92.0], [33.3, 92.0], [33.4, 92.0], [33.5, 92.0], [33.6, 92.0], [33.7, 92.0], [33.8, 92.0], [33.9, 92.0], [34.0, 92.0], [34.1, 92.0], [34.2, 93.0], [34.3, 93.0], [34.4, 93.0], [34.5, 93.0], [34.6, 93.0], [34.7, 93.0], [34.8, 93.0], [34.9, 93.0], [35.0, 93.0], [35.1, 93.0], [35.2, 93.0], [35.3, 93.0], [35.4, 93.0], [35.5, 93.0], [35.6, 93.0], [35.7, 94.0], [35.8, 94.0], [35.9, 94.0], [36.0, 94.0], [36.1, 94.0], [36.2, 94.0], [36.3, 94.0], [36.4, 94.0], [36.5, 94.0], [36.6, 94.0], [36.7, 94.0], [36.8, 94.0], [36.9, 94.0], [37.0, 94.0], [37.1, 94.0], [37.2, 95.0], [37.3, 95.0], [37.4, 95.0], [37.5, 95.0], [37.6, 95.0], [37.7, 95.0], [37.8, 95.0], [37.9, 95.0], [38.0, 95.0], [38.1, 95.0], [38.2, 96.0], [38.3, 96.0], [38.4, 96.0], [38.5, 96.0], [38.6, 96.0], [38.7, 96.0], [38.8, 96.0], [38.9, 96.0], [39.0, 96.0], [39.1, 96.0], [39.2, 96.0], [39.3, 96.0], [39.4, 96.0], [39.5, 96.0], [39.6, 96.0], [39.7, 97.0], [39.8, 97.0], [39.9, 97.0], [40.0, 97.0], [40.1, 97.0], [40.2, 97.0], [40.3, 97.0], [40.4, 97.0], [40.5, 97.0], [40.6, 97.0], [40.7, 97.0], [40.8, 98.0], [40.9, 98.0], [41.0, 98.0], [41.1, 98.0], [41.2, 98.0], [41.3, 98.0], [41.4, 98.0], [41.5, 98.0], [41.6, 98.0], [41.7, 98.0], [41.8, 98.0], [41.9, 98.0], [42.0, 98.0], [42.1, 99.0], [42.2, 99.0], [42.3, 99.0], [42.4, 99.0], [42.5, 99.0], [42.6, 99.0], [42.7, 99.0], [42.8, 99.0], [42.9, 99.0], [43.0, 99.0], [43.1, 99.0], [43.2, 99.0], [43.3, 99.0], [43.4, 100.0], [43.5, 100.0], [43.6, 100.0], [43.7, 100.0], [43.8, 100.0], [43.9, 100.0], [44.0, 100.0], [44.1, 100.0], [44.2, 100.0], [44.3, 100.0], [44.4, 100.0], [44.5, 100.0], [44.6, 101.0], [44.7, 101.0], [44.8, 101.0], [44.9, 101.0], [45.0, 101.0], [45.1, 101.0], [45.2, 101.0], [45.3, 101.0], [45.4, 101.0], [45.5, 101.0], [45.6, 101.0], [45.7, 101.0], [45.8, 101.0], [45.9, 102.0], [46.0, 102.0], [46.1, 102.0], [46.2, 102.0], [46.3, 102.0], [46.4, 102.0], [46.5, 102.0], [46.6, 102.0], [46.7, 102.0], [46.8, 102.0], [46.9, 102.0], [47.0, 102.0], [47.1, 102.0], [47.2, 103.0], [47.3, 103.0], [47.4, 103.0], [47.5, 103.0], [47.6, 103.0], [47.7, 103.0], [47.8, 103.0], [47.9, 103.0], [48.0, 103.0], [48.1, 103.0], [48.2, 103.0], [48.3, 103.0], [48.4, 104.0], [48.5, 104.0], [48.6, 104.0], [48.7, 104.0], [48.8, 104.0], [48.9, 104.0], [49.0, 104.0], [49.1, 104.0], [49.2, 104.0], [49.3, 105.0], [49.4, 105.0], [49.5, 105.0], [49.6, 105.0], [49.7, 105.0], [49.8, 105.0], [49.9, 105.0], [50.0, 106.0], [50.1, 106.0], [50.2, 106.0], [50.3, 106.0], [50.4, 106.0], [50.5, 106.0], [50.6, 107.0], [50.7, 107.0], [50.8, 107.0], [50.9, 107.0], [51.0, 107.0], [51.1, 107.0], [51.2, 108.0], [51.3, 108.0], [51.4, 108.0], [51.5, 108.0], [51.6, 108.0], [51.7, 108.0], [51.8, 109.0], [51.9, 109.0], [52.0, 109.0], [52.1, 109.0], [52.2, 109.0], [52.3, 109.0], [52.4, 109.0], [52.5, 110.0], [52.6, 110.0], [52.7, 110.0], [52.8, 110.0], [52.9, 110.0], [53.0, 111.0], [53.1, 111.0], [53.2, 111.0], [53.3, 111.0], [53.4, 111.0], [53.5, 112.0], [53.6, 112.0], [53.7, 113.0], [53.8, 113.0], [53.9, 113.0], [54.0, 113.0], [54.1, 113.0], [54.2, 114.0], [54.3, 114.0], [54.4, 114.0], [54.5, 115.0], [54.6, 115.0], [54.7, 115.0], [54.8, 116.0], [54.9, 116.0], [55.0, 116.0], [55.1, 116.0], [55.2, 116.0], [55.3, 116.0], [55.4, 117.0], [55.5, 117.0], [55.6, 117.0], [55.7, 117.0], [55.8, 118.0], [55.9, 118.0], [56.0, 118.0], [56.1, 118.0], [56.2, 118.0], [56.3, 119.0], [56.4, 119.0], [56.5, 119.0], [56.6, 119.0], [56.7, 120.0], [56.8, 120.0], [56.9, 120.0], [57.0, 120.0], [57.1, 120.0], [57.2, 120.0], [57.3, 121.0], [57.4, 121.0], [57.5, 122.0], [57.6, 122.0], [57.7, 122.0], [57.8, 122.0], [57.9, 123.0], [58.0, 123.0], [58.1, 124.0], [58.2, 125.0], [58.3, 126.0], [58.4, 126.0], [58.5, 127.0], [58.6, 127.0], [58.7, 127.0], [58.8, 128.0], [58.9, 128.0], [59.0, 128.0], [59.1, 128.0], [59.2, 129.0], [59.3, 129.0], [59.4, 130.0], [59.5, 130.0], [59.6, 130.0], [59.7, 130.0], [59.8, 131.0], [59.9, 131.0], [60.0, 131.0], [60.1, 133.0], [60.2, 133.0], [60.3, 133.0], [60.4, 133.0], [60.5, 134.0], [60.6, 134.0], [60.7, 135.0], [60.8, 135.0], [60.9, 135.0], [61.0, 136.0], [61.1, 136.0], [61.2, 136.0], [61.3, 137.0], [61.4, 137.0], [61.5, 137.0], [61.6, 137.0], [61.7, 138.0], [61.8, 139.0], [61.9, 139.0], [62.0, 139.0], [62.1, 140.0], [62.2, 140.0], [62.3, 140.0], [62.4, 141.0], [62.5, 141.0], [62.6, 141.0], [62.7, 142.0], [62.8, 143.0], [62.9, 143.0], [63.0, 143.0], [63.1, 144.0], [63.2, 144.0], [63.3, 145.0], [63.4, 145.0], [63.5, 145.0], [63.6, 146.0], [63.7, 146.0], [63.8, 147.0], [63.9, 147.0], [64.0, 149.0], [64.1, 149.0], [64.2, 149.0], [64.3, 150.0], [64.4, 150.0], [64.5, 150.0], [64.6, 151.0], [64.7, 152.0], [64.8, 152.0], [64.9, 152.0], [65.0, 153.0], [65.1, 153.0], [65.2, 153.0], [65.3, 155.0], [65.4, 155.0], [65.5, 155.0], [65.6, 155.0], [65.7, 157.0], [65.8, 158.0], [65.9, 160.0], [66.0, 162.0], [66.1, 163.0], [66.2, 163.0], [66.3, 165.0], [66.4, 166.0], [66.5, 167.0], [66.6, 167.0], [66.7, 167.0], [66.8, 168.0], [66.9, 168.0], [67.0, 168.0], [67.1, 169.0], [67.2, 170.0], [67.3, 170.0], [67.4, 170.0], [67.5, 170.0], [67.6, 171.0], [67.7, 172.0], [67.8, 172.0], [67.9, 173.0], [68.0, 174.0], [68.1, 174.0], [68.2, 174.0], [68.3, 176.0], [68.4, 177.0], [68.5, 177.0], [68.6, 179.0], [68.7, 179.0], [68.8, 179.0], [68.9, 180.0], [69.0, 181.0], [69.1, 182.0], [69.2, 182.0], [69.3, 182.0], [69.4, 182.0], [69.5, 182.0], [69.6, 184.0], [69.7, 186.0], [69.8, 187.0], [69.9, 187.0], [70.0, 188.0], [70.1, 188.0], [70.2, 188.0], [70.3, 191.0], [70.4, 192.0], [70.5, 193.0], [70.6, 197.0], [70.7, 199.0], [70.8, 200.0], [70.9, 200.0], [71.0, 201.0], [71.1, 201.0], [71.2, 202.0], [71.3, 202.0], [71.4, 203.0], [71.5, 203.0], [71.6, 205.0], [71.7, 205.0], [71.8, 205.0], [71.9, 207.0], [72.0, 208.0], [72.1, 208.0], [72.2, 209.0], [72.3, 209.0], [72.4, 210.0], [72.5, 210.0], [72.6, 212.0], [72.7, 215.0], [72.8, 217.0], [72.9, 217.0], [73.0, 217.0], [73.1, 217.0], [73.2, 218.0], [73.3, 219.0], [73.4, 220.0], [73.5, 220.0], [73.6, 220.0], [73.7, 220.0], [73.8, 223.0], [73.9, 224.0], [74.0, 224.0], [74.1, 225.0], [74.2, 226.0], [74.3, 229.0], [74.4, 229.0], [74.5, 229.0], [74.6, 229.0], [74.7, 230.0], [74.8, 233.0], [74.9, 234.0], [75.0, 234.0], [75.1, 234.0], [75.2, 235.0], [75.3, 235.0], [75.4, 237.0], [75.5, 237.0], [75.6, 238.0], [75.7, 239.0], [75.8, 240.0], [75.9, 240.0], [76.0, 240.0], [76.1, 240.0], [76.2, 241.0], [76.3, 243.0], [76.4, 246.0], [76.5, 246.0], [76.6, 248.0], [76.7, 248.0], [76.8, 249.0], [76.9, 250.0], [77.0, 250.0], [77.1, 252.0], [77.2, 253.0], [77.3, 253.0], [77.4, 253.0], [77.5, 254.0], [77.6, 255.0], [77.7, 256.0], [77.8, 257.0], [77.9, 257.0], [78.0, 258.0], [78.1, 259.0], [78.2, 262.0], [78.3, 263.0], [78.4, 263.0], [78.5, 267.0], [78.6, 269.0], [78.7, 272.0], [78.8, 274.0], [78.9, 275.0], [79.0, 276.0], [79.1, 276.0], [79.2, 277.0], [79.3, 279.0], [79.4, 280.0], [79.5, 280.0], [79.6, 281.0], [79.7, 282.0], [79.8, 283.0], [79.9, 283.0], [80.0, 286.0], [80.1, 286.0], [80.2, 288.0], [80.3, 290.0], [80.4, 291.0], [80.5, 291.0], [80.6, 294.0], [80.7, 295.0], [80.8, 295.0], [80.9, 295.0], [81.0, 295.0], [81.1, 298.0], [81.2, 300.0], [81.3, 302.0], [81.4, 303.0], [81.5, 308.0], [81.6, 310.0], [81.7, 313.0], [81.8, 315.0], [81.9, 316.0], [82.0, 321.0], [82.1, 321.0], [82.2, 321.0], [82.3, 322.0], [82.4, 323.0], [82.5, 327.0], [82.6, 328.0], [82.7, 329.0], [82.8, 329.0], [82.9, 330.0], [83.0, 331.0], [83.1, 336.0], [83.2, 337.0], [83.3, 338.0], [83.4, 339.0], [83.5, 339.0], [83.6, 343.0], [83.7, 343.0], [83.8, 344.0], [83.9, 344.0], [84.0, 345.0], [84.1, 346.0], [84.2, 347.0], [84.3, 347.0], [84.4, 350.0], [84.5, 354.0], [84.6, 355.0], [84.7, 355.0], [84.8, 358.0], [84.9, 359.0], [85.0, 361.0], [85.1, 361.0], [85.2, 362.0], [85.3, 362.0], [85.4, 362.0], [85.5, 362.0], [85.6, 363.0], [85.7, 364.0], [85.8, 366.0], [85.9, 367.0], [86.0, 370.0], [86.1, 374.0], [86.2, 378.0], [86.3, 378.0], [86.4, 381.0], [86.5, 385.0], [86.6, 386.0], [86.7, 392.0], [86.8, 401.0], [86.9, 406.0], [87.0, 406.0], [87.1, 410.0], [87.2, 410.0], [87.3, 411.0], [87.4, 416.0], [87.5, 420.0], [87.6, 422.0], [87.7, 422.0], [87.8, 423.0], [87.9, 424.0], [88.0, 424.0], [88.1, 426.0], [88.2, 428.0], [88.3, 431.0], [88.4, 439.0], [88.5, 445.0], [88.6, 445.0], [88.7, 447.0], [88.8, 450.0], [88.9, 453.0], [89.0, 456.0], [89.1, 458.0], [89.2, 458.0], [89.3, 459.0], [89.4, 464.0], [89.5, 464.0], [89.6, 464.0], [89.7, 466.0], [89.8, 466.0], [89.9, 469.0], [90.0, 469.0], [90.1, 470.0], [90.2, 471.0], [90.3, 471.0], [90.4, 471.0], [90.5, 474.0], [90.6, 475.0], [90.7, 475.0], [90.8, 479.0], [90.9, 488.0], [91.0, 488.0], [91.1, 492.0], [91.2, 493.0], [91.3, 493.0], [91.4, 496.0], [91.5, 499.0], [91.6, 499.0], [91.7, 500.0], [91.8, 501.0], [91.9, 506.0], [92.0, 512.0], [92.1, 518.0], [92.2, 519.0], [92.3, 520.0], [92.4, 520.0], [92.5, 526.0], [92.6, 530.0], [92.7, 533.0], [92.8, 534.0], [92.9, 536.0], [93.0, 536.0], [93.1, 539.0], [93.2, 556.0], [93.3, 558.0], [93.4, 564.0], [93.5, 569.0], [93.6, 569.0], [93.7, 570.0], [93.8, 585.0], [93.9, 589.0], [94.0, 597.0], [94.1, 602.0], [94.2, 604.0], [94.3, 608.0], [94.4, 608.0], [94.5, 613.0], [94.6, 615.0], [94.7, 616.0], [94.8, 617.0], [94.9, 617.0], [95.0, 624.0], [95.1, 627.0], [95.2, 631.0], [95.3, 631.0], [95.4, 640.0], [95.5, 644.0], [95.6, 645.0], [95.7, 646.0], [95.8, 646.0], [95.9, 656.0], [96.0, 661.0], [96.1, 663.0], [96.2, 669.0], [96.3, 688.0], [96.4, 706.0], [96.5, 709.0], [96.6, 711.0], [96.7, 712.0], [96.8, 722.0], [96.9, 732.0], [97.0, 747.0], [97.1, 753.0], [97.2, 753.0], [97.3, 779.0], [97.4, 781.0], [97.5, 781.0], [97.6, 836.0], [97.7, 854.0], [97.8, 862.0], [97.9, 862.0], [98.0, 866.0], [98.1, 882.0], [98.2, 900.0], [98.3, 922.0], [98.4, 933.0], [98.5, 935.0], [98.6, 958.0], [98.7, 974.0], [98.8, 978.0], [98.9, 989.0], [99.0, 996.0], [99.1, 1018.0], [99.2, 1020.0], [99.3, 1020.0], [99.4, 1059.0], [99.5, 1086.0], [99.6, 1090.0], [99.7, 1106.0], [99.8, 1124.0], [99.9, 1180.0]], "isOverall": false, "label": "Dashboard", "isController": false}, {"data": [[0.0, 52.0], [0.1, 52.0], [0.2, 53.0], [0.3, 53.0], [0.4, 53.0], [0.5, 53.0], [0.6, 54.0], [0.7, 54.0], [0.8, 54.0], [0.9, 54.0], [1.0, 54.0], [1.1, 54.0], [1.2, 54.0], [1.3, 54.0], [1.4, 54.0], [1.5, 54.0], [1.6, 54.0], [1.7, 55.0], [1.8, 55.0], [1.9, 55.0], [2.0, 55.0], [2.1, 55.0], [2.2, 55.0], [2.3, 55.0], [2.4, 55.0], [2.5, 55.0], [2.6, 55.0], [2.7, 55.0], [2.8, 55.0], [2.9, 55.0], [3.0, 55.0], [3.1, 56.0], [3.2, 56.0], [3.3, 56.0], [3.4, 56.0], [3.5, 56.0], [3.6, 56.0], [3.7, 56.0], [3.8, 56.0], [3.9, 56.0], [4.0, 56.0], [4.1, 56.0], [4.2, 56.0], [4.3, 56.0], [4.4, 56.0], [4.5, 56.0], [4.6, 56.0], [4.7, 57.0], [4.8, 57.0], [4.9, 57.0], [5.0, 57.0], [5.1, 57.0], [5.2, 57.0], [5.3, 57.0], [5.4, 57.0], [5.5, 57.0], [5.6, 57.0], [5.7, 57.0], [5.8, 57.0], [5.9, 57.0], [6.0, 57.0], [6.1, 57.0], [6.2, 57.0], [6.3, 57.0], [6.4, 57.0], [6.5, 57.0], [6.6, 57.0], [6.7, 57.0], [6.8, 57.0], [6.9, 57.0], [7.0, 57.0], [7.1, 57.0], [7.2, 57.0], [7.3, 57.0], [7.4, 58.0], [7.5, 58.0], [7.6, 58.0], [7.7, 58.0], [7.8, 58.0], [7.9, 58.0], [8.0, 58.0], [8.1, 58.0], [8.2, 58.0], [8.3, 58.0], [8.4, 58.0], [8.5, 58.0], [8.6, 58.0], [8.7, 58.0], [8.8, 58.0], [8.9, 58.0], [9.0, 58.0], [9.1, 58.0], [9.2, 58.0], [9.3, 58.0], [9.4, 58.0], [9.5, 58.0], [9.6, 58.0], [9.7, 58.0], [9.8, 58.0], [9.9, 58.0], [10.0, 58.0], [10.1, 58.0], [10.2, 58.0], [10.3, 58.0], [10.4, 58.0], [10.5, 58.0], [10.6, 59.0], [10.7, 59.0], [10.8, 59.0], [10.9, 59.0], [11.0, 59.0], [11.1, 59.0], [11.2, 59.0], [11.3, 59.0], [11.4, 59.0], [11.5, 59.0], [11.6, 59.0], [11.7, 59.0], [11.8, 59.0], [11.9, 59.0], [12.0, 59.0], [12.1, 59.0], [12.2, 59.0], [12.3, 59.0], [12.4, 59.0], [12.5, 59.0], [12.6, 59.0], [12.7, 59.0], [12.8, 59.0], [12.9, 59.0], [13.0, 59.0], [13.1, 60.0], [13.2, 60.0], [13.3, 60.0], [13.4, 60.0], [13.5, 60.0], [13.6, 60.0], [13.7, 60.0], [13.8, 60.0], [13.9, 60.0], [14.0, 60.0], [14.1, 60.0], [14.2, 60.0], [14.3, 60.0], [14.4, 60.0], [14.5, 60.0], [14.6, 60.0], [14.7, 60.0], [14.8, 60.0], [14.9, 60.0], [15.0, 60.0], [15.1, 60.0], [15.2, 60.0], [15.3, 60.0], [15.4, 60.0], [15.5, 60.0], [15.6, 60.0], [15.7, 60.0], [15.8, 60.0], [15.9, 60.0], [16.0, 60.0], [16.1, 60.0], [16.2, 60.0], [16.3, 60.0], [16.4, 60.0], [16.5, 60.0], [16.6, 61.0], [16.7, 61.0], [16.8, 61.0], [16.9, 61.0], [17.0, 61.0], [17.1, 61.0], [17.2, 61.0], [17.3, 61.0], [17.4, 61.0], [17.5, 61.0], [17.6, 61.0], [17.7, 61.0], [17.8, 61.0], [17.9, 61.0], [18.0, 61.0], [18.1, 61.0], [18.2, 61.0], [18.3, 62.0], [18.4, 62.0], [18.5, 62.0], [18.6, 62.0], [18.7, 62.0], [18.8, 62.0], [18.9, 62.0], [19.0, 62.0], [19.1, 62.0], [19.2, 62.0], [19.3, 62.0], [19.4, 62.0], [19.5, 63.0], [19.6, 63.0], [19.7, 63.0], [19.8, 63.0], [19.9, 63.0], [20.0, 63.0], [20.1, 63.0], [20.2, 63.0], [20.3, 63.0], [20.4, 63.0], [20.5, 63.0], [20.6, 63.0], [20.7, 63.0], [20.8, 63.0], [20.9, 63.0], [21.0, 63.0], [21.1, 64.0], [21.2, 64.0], [21.3, 64.0], [21.4, 64.0], [21.5, 64.0], [21.6, 64.0], [21.7, 65.0], [21.8, 65.0], [21.9, 65.0], [22.0, 65.0], [22.1, 65.0], [22.2, 65.0], [22.3, 65.0], [22.4, 65.0], [22.5, 65.0], [22.6, 65.0], [22.7, 65.0], [22.8, 65.0], [22.9, 65.0], [23.0, 65.0], [23.1, 65.0], [23.2, 65.0], [23.3, 66.0], [23.4, 66.0], [23.5, 66.0], [23.6, 66.0], [23.7, 66.0], [23.8, 66.0], [23.9, 66.0], [24.0, 66.0], [24.1, 66.0], [24.2, 66.0], [24.3, 66.0], [24.4, 66.0], [24.5, 67.0], [24.6, 67.0], [24.7, 67.0], [24.8, 67.0], [24.9, 67.0], [25.0, 67.0], [25.1, 67.0], [25.2, 67.0], [25.3, 68.0], [25.4, 68.0], [25.5, 68.0], [25.6, 68.0], [25.7, 68.0], [25.8, 68.0], [25.9, 68.0], [26.0, 68.0], [26.1, 68.0], [26.2, 68.0], [26.3, 68.0], [26.4, 68.0], [26.5, 69.0], [26.6, 69.0], [26.7, 69.0], [26.8, 69.0], [26.9, 69.0], [27.0, 69.0], [27.1, 69.0], [27.2, 69.0], [27.3, 69.0], [27.4, 70.0], [27.5, 70.0], [27.6, 70.0], [27.7, 70.0], [27.8, 70.0], [27.9, 70.0], [28.0, 70.0], [28.1, 70.0], [28.2, 70.0], [28.3, 70.0], [28.4, 70.0], [28.5, 70.0], [28.6, 70.0], [28.7, 70.0], [28.8, 70.0], [28.9, 71.0], [29.0, 71.0], [29.1, 71.0], [29.2, 71.0], [29.3, 71.0], [29.4, 71.0], [29.5, 72.0], [29.6, 72.0], [29.7, 72.0], [29.8, 72.0], [29.9, 72.0], [30.0, 72.0], [30.1, 72.0], [30.2, 72.0], [30.3, 72.0], [30.4, 72.0], [30.5, 72.0], [30.6, 73.0], [30.7, 73.0], [30.8, 73.0], [30.9, 73.0], [31.0, 73.0], [31.1, 73.0], [31.2, 74.0], [31.3, 74.0], [31.4, 74.0], [31.5, 74.0], [31.6, 74.0], [31.7, 74.0], [31.8, 74.0], [31.9, 74.0], [32.0, 74.0], [32.1, 74.0], [32.2, 74.0], [32.3, 74.0], [32.4, 74.0], [32.5, 74.0], [32.6, 74.0], [32.7, 75.0], [32.8, 75.0], [32.9, 75.0], [33.0, 75.0], [33.1, 75.0], [33.2, 75.0], [33.3, 75.0], [33.4, 75.0], [33.5, 75.0], [33.6, 75.0], [33.7, 76.0], [33.8, 76.0], [33.9, 76.0], [34.0, 76.0], [34.1, 76.0], [34.2, 76.0], [34.3, 76.0], [34.4, 76.0], [34.5, 76.0], [34.6, 76.0], [34.7, 76.0], [34.8, 76.0], [34.9, 77.0], [35.0, 77.0], [35.1, 77.0], [35.2, 77.0], [35.3, 77.0], [35.4, 77.0], [35.5, 77.0], [35.6, 77.0], [35.7, 77.0], [35.8, 77.0], [35.9, 77.0], [36.0, 77.0], [36.1, 77.0], [36.2, 78.0], [36.3, 78.0], [36.4, 78.0], [36.5, 78.0], [36.6, 78.0], [36.7, 78.0], [36.8, 78.0], [36.9, 78.0], [37.0, 78.0], [37.1, 78.0], [37.2, 79.0], [37.3, 79.0], [37.4, 79.0], [37.5, 79.0], [37.6, 79.0], [37.7, 80.0], [37.8, 80.0], [37.9, 80.0], [38.0, 80.0], [38.1, 80.0], [38.2, 81.0], [38.3, 81.0], [38.4, 81.0], [38.5, 81.0], [38.6, 81.0], [38.7, 81.0], [38.8, 81.0], [38.9, 81.0], [39.0, 81.0], [39.1, 81.0], [39.2, 81.0], [39.3, 81.0], [39.4, 82.0], [39.5, 82.0], [39.6, 82.0], [39.7, 82.0], [39.8, 82.0], [39.9, 82.0], [40.0, 82.0], [40.1, 82.0], [40.2, 82.0], [40.3, 82.0], [40.4, 82.0], [40.5, 82.0], [40.6, 82.0], [40.7, 83.0], [40.8, 83.0], [40.9, 83.0], [41.0, 83.0], [41.1, 83.0], [41.2, 83.0], [41.3, 83.0], [41.4, 83.0], [41.5, 83.0], [41.6, 83.0], [41.7, 84.0], [41.8, 84.0], [41.9, 84.0], [42.0, 84.0], [42.1, 84.0], [42.2, 84.0], [42.3, 84.0], [42.4, 84.0], [42.5, 84.0], [42.6, 84.0], [42.7, 84.0], [42.8, 84.0], [42.9, 84.0], [43.0, 85.0], [43.1, 85.0], [43.2, 85.0], [43.3, 85.0], [43.4, 85.0], [43.5, 85.0], [43.6, 85.0], [43.7, 85.0], [43.8, 85.0], [43.9, 86.0], [44.0, 86.0], [44.1, 86.0], [44.2, 86.0], [44.3, 86.0], [44.4, 87.0], [44.5, 87.0], [44.6, 87.0], [44.7, 87.0], [44.8, 87.0], [44.9, 87.0], [45.0, 87.0], [45.1, 87.0], [45.2, 87.0], [45.3, 88.0], [45.4, 88.0], [45.5, 88.0], [45.6, 88.0], [45.7, 89.0], [45.8, 89.0], [45.9, 89.0], [46.0, 89.0], [46.1, 89.0], [46.2, 89.0], [46.3, 89.0], [46.4, 89.0], [46.5, 89.0], [46.6, 89.0], [46.7, 90.0], [46.8, 90.0], [46.9, 90.0], [47.0, 90.0], [47.1, 90.0], [47.2, 91.0], [47.3, 91.0], [47.4, 91.0], [47.5, 91.0], [47.6, 91.0], [47.7, 91.0], [47.8, 91.0], [47.9, 91.0], [48.0, 92.0], [48.1, 92.0], [48.2, 92.0], [48.3, 92.0], [48.4, 92.0], [48.5, 93.0], [48.6, 93.0], [48.7, 93.0], [48.8, 94.0], [48.9, 94.0], [49.0, 94.0], [49.1, 96.0], [49.2, 96.0], [49.3, 96.0], [49.4, 97.0], [49.5, 97.0], [49.6, 97.0], [49.7, 97.0], [49.8, 97.0], [49.9, 98.0], [50.0, 98.0], [50.1, 98.0], [50.2, 98.0], [50.3, 98.0], [50.4, 98.0], [50.5, 98.0], [50.6, 99.0], [50.7, 99.0], [50.8, 100.0], [50.9, 101.0], [51.0, 101.0], [51.1, 101.0], [51.2, 102.0], [51.3, 102.0], [51.4, 102.0], [51.5, 102.0], [51.6, 103.0], [51.7, 103.0], [51.8, 103.0], [51.9, 103.0], [52.0, 103.0], [52.1, 104.0], [52.2, 104.0], [52.3, 104.0], [52.4, 104.0], [52.5, 105.0], [52.6, 105.0], [52.7, 105.0], [52.8, 105.0], [52.9, 105.0], [53.0, 106.0], [53.1, 106.0], [53.2, 106.0], [53.3, 106.0], [53.4, 106.0], [53.5, 107.0], [53.6, 107.0], [53.7, 108.0], [53.8, 108.0], [53.9, 108.0], [54.0, 108.0], [54.1, 108.0], [54.2, 109.0], [54.3, 109.0], [54.4, 110.0], [54.5, 110.0], [54.6, 110.0], [54.7, 111.0], [54.8, 111.0], [54.9, 111.0], [55.0, 111.0], [55.1, 111.0], [55.2, 112.0], [55.3, 112.0], [55.4, 112.0], [55.5, 113.0], [55.6, 113.0], [55.7, 114.0], [55.8, 114.0], [55.9, 114.0], [56.0, 115.0], [56.1, 115.0], [56.2, 115.0], [56.3, 115.0], [56.4, 116.0], [56.5, 116.0], [56.6, 117.0], [56.7, 117.0], [56.8, 117.0], [56.9, 117.0], [57.0, 117.0], [57.1, 118.0], [57.2, 118.0], [57.3, 118.0], [57.4, 119.0], [57.5, 119.0], [57.6, 119.0], [57.7, 120.0], [57.8, 121.0], [57.9, 121.0], [58.0, 121.0], [58.1, 121.0], [58.2, 122.0], [58.3, 122.0], [58.4, 123.0], [58.5, 123.0], [58.6, 125.0], [58.7, 125.0], [58.8, 126.0], [58.9, 127.0], [59.0, 127.0], [59.1, 127.0], [59.2, 128.0], [59.3, 128.0], [59.4, 128.0], [59.5, 129.0], [59.6, 129.0], [59.7, 129.0], [59.8, 130.0], [59.9, 130.0], [60.0, 130.0], [60.1, 130.0], [60.2, 130.0], [60.3, 131.0], [60.4, 131.0], [60.5, 131.0], [60.6, 132.0], [60.7, 133.0], [60.8, 133.0], [60.9, 134.0], [61.0, 134.0], [61.1, 134.0], [61.2, 134.0], [61.3, 134.0], [61.4, 136.0], [61.5, 136.0], [61.6, 137.0], [61.7, 138.0], [61.8, 139.0], [61.9, 141.0], [62.0, 141.0], [62.1, 141.0], [62.2, 141.0], [62.3, 142.0], [62.4, 142.0], [62.5, 143.0], [62.6, 143.0], [62.7, 143.0], [62.8, 144.0], [62.9, 146.0], [63.0, 146.0], [63.1, 146.0], [63.2, 146.0], [63.3, 146.0], [63.4, 146.0], [63.5, 147.0], [63.6, 147.0], [63.7, 147.0], [63.8, 147.0], [63.9, 148.0], [64.0, 148.0], [64.1, 149.0], [64.2, 150.0], [64.3, 151.0], [64.4, 153.0], [64.5, 154.0], [64.6, 154.0], [64.7, 155.0], [64.8, 155.0], [64.9, 155.0], [65.0, 157.0], [65.1, 157.0], [65.2, 158.0], [65.3, 158.0], [65.4, 159.0], [65.5, 159.0], [65.6, 159.0], [65.7, 160.0], [65.8, 160.0], [65.9, 160.0], [66.0, 162.0], [66.1, 163.0], [66.2, 165.0], [66.3, 165.0], [66.4, 166.0], [66.5, 166.0], [66.6, 169.0], [66.7, 169.0], [66.8, 169.0], [66.9, 171.0], [67.0, 171.0], [67.1, 173.0], [67.2, 173.0], [67.3, 175.0], [67.4, 176.0], [67.5, 176.0], [67.6, 177.0], [67.7, 177.0], [67.8, 178.0], [67.9, 179.0], [68.0, 179.0], [68.1, 180.0], [68.2, 181.0], [68.3, 183.0], [68.4, 183.0], [68.5, 184.0], [68.6, 184.0], [68.7, 185.0], [68.8, 185.0], [68.9, 185.0], [69.0, 186.0], [69.1, 187.0], [69.2, 187.0], [69.3, 188.0], [69.4, 189.0], [69.5, 189.0], [69.6, 189.0], [69.7, 192.0], [69.8, 192.0], [69.9, 193.0], [70.0, 193.0], [70.1, 195.0], [70.2, 195.0], [70.3, 196.0], [70.4, 196.0], [70.5, 196.0], [70.6, 201.0], [70.7, 201.0], [70.8, 201.0], [70.9, 203.0], [71.0, 204.0], [71.1, 204.0], [71.2, 204.0], [71.3, 205.0], [71.4, 207.0], [71.5, 208.0], [71.6, 208.0], [71.7, 208.0], [71.8, 210.0], [71.9, 211.0], [72.0, 211.0], [72.1, 212.0], [72.2, 213.0], [72.3, 213.0], [72.4, 213.0], [72.5, 215.0], [72.6, 215.0], [72.7, 215.0], [72.8, 216.0], [72.9, 216.0], [73.0, 216.0], [73.1, 217.0], [73.2, 218.0], [73.3, 219.0], [73.4, 219.0], [73.5, 221.0], [73.6, 223.0], [73.7, 223.0], [73.8, 224.0], [73.9, 224.0], [74.0, 225.0], [74.1, 225.0], [74.2, 227.0], [74.3, 228.0], [74.4, 229.0], [74.5, 229.0], [74.6, 231.0], [74.7, 231.0], [74.8, 232.0], [74.9, 234.0], [75.0, 236.0], [75.1, 236.0], [75.2, 237.0], [75.3, 238.0], [75.4, 239.0], [75.5, 240.0], [75.6, 241.0], [75.7, 242.0], [75.8, 242.0], [75.9, 243.0], [76.0, 243.0], [76.1, 247.0], [76.2, 248.0], [76.3, 250.0], [76.4, 250.0], [76.5, 250.0], [76.6, 254.0], [76.7, 254.0], [76.8, 254.0], [76.9, 256.0], [77.0, 257.0], [77.1, 260.0], [77.2, 260.0], [77.3, 265.0], [77.4, 269.0], [77.5, 270.0], [77.6, 271.0], [77.7, 272.0], [77.8, 272.0], [77.9, 272.0], [78.0, 273.0], [78.1, 275.0], [78.2, 276.0], [78.3, 276.0], [78.4, 277.0], [78.5, 280.0], [78.6, 282.0], [78.7, 283.0], [78.8, 284.0], [78.9, 284.0], [79.0, 285.0], [79.1, 286.0], [79.2, 286.0], [79.3, 287.0], [79.4, 292.0], [79.5, 292.0], [79.6, 293.0], [79.7, 293.0], [79.8, 294.0], [79.9, 294.0], [80.0, 295.0], [80.1, 297.0], [80.2, 298.0], [80.3, 301.0], [80.4, 303.0], [80.5, 306.0], [80.6, 308.0], [80.7, 311.0], [80.8, 311.0], [80.9, 313.0], [81.0, 313.0], [81.1, 314.0], [81.2, 315.0], [81.3, 315.0], [81.4, 317.0], [81.5, 317.0], [81.6, 318.0], [81.7, 318.0], [81.8, 319.0], [81.9, 320.0], [82.0, 322.0], [82.1, 325.0], [82.2, 326.0], [82.3, 329.0], [82.4, 336.0], [82.5, 342.0], [82.6, 343.0], [82.7, 344.0], [82.8, 344.0], [82.9, 344.0], [83.0, 344.0], [83.1, 349.0], [83.2, 349.0], [83.3, 357.0], [83.4, 357.0], [83.5, 358.0], [83.6, 359.0], [83.7, 359.0], [83.8, 361.0], [83.9, 362.0], [84.0, 364.0], [84.1, 367.0], [84.2, 373.0], [84.3, 374.0], [84.4, 377.0], [84.5, 380.0], [84.6, 386.0], [84.7, 386.0], [84.8, 387.0], [84.9, 387.0], [85.0, 387.0], [85.1, 388.0], [85.2, 388.0], [85.3, 390.0], [85.4, 393.0], [85.5, 393.0], [85.6, 397.0], [85.7, 403.0], [85.8, 405.0], [85.9, 408.0], [86.0, 409.0], [86.1, 411.0], [86.2, 414.0], [86.3, 419.0], [86.4, 424.0], [86.5, 425.0], [86.6, 426.0], [86.7, 428.0], [86.8, 429.0], [86.9, 431.0], [87.0, 434.0], [87.1, 434.0], [87.2, 436.0], [87.3, 439.0], [87.4, 441.0], [87.5, 441.0], [87.6, 447.0], [87.7, 449.0], [87.8, 450.0], [87.9, 451.0], [88.0, 457.0], [88.1, 459.0], [88.2, 467.0], [88.3, 468.0], [88.4, 468.0], [88.5, 470.0], [88.6, 473.0], [88.7, 473.0], [88.8, 474.0], [88.9, 474.0], [89.0, 482.0], [89.1, 485.0], [89.2, 486.0], [89.3, 486.0], [89.4, 493.0], [89.5, 496.0], [89.6, 498.0], [89.7, 500.0], [89.8, 504.0], [89.9, 505.0], [90.0, 506.0], [90.1, 507.0], [90.2, 507.0], [90.3, 512.0], [90.4, 513.0], [90.5, 516.0], [90.6, 523.0], [90.7, 524.0], [90.8, 524.0], [90.9, 525.0], [91.0, 527.0], [91.1, 531.0], [91.2, 531.0], [91.3, 531.0], [91.4, 535.0], [91.5, 538.0], [91.6, 539.0], [91.7, 539.0], [91.8, 540.0], [91.9, 543.0], [92.0, 545.0], [92.1, 546.0], [92.2, 550.0], [92.3, 551.0], [92.4, 553.0], [92.5, 555.0], [92.6, 555.0], [92.7, 560.0], [92.8, 566.0], [92.9, 567.0], [93.0, 573.0], [93.1, 585.0], [93.2, 590.0], [93.3, 596.0], [93.4, 599.0], [93.5, 614.0], [93.6, 615.0], [93.7, 623.0], [93.8, 624.0], [93.9, 625.0], [94.0, 628.0], [94.1, 630.0], [94.2, 634.0], [94.3, 636.0], [94.4, 637.0], [94.5, 637.0], [94.6, 641.0], [94.7, 647.0], [94.8, 656.0], [94.9, 657.0], [95.0, 662.0], [95.1, 671.0], [95.2, 680.0], [95.3, 684.0], [95.4, 688.0], [95.5, 692.0], [95.6, 693.0], [95.7, 697.0], [95.8, 712.0], [95.9, 718.0], [96.0, 718.0], [96.1, 723.0], [96.2, 729.0], [96.3, 729.0], [96.4, 730.0], [96.5, 739.0], [96.6, 750.0], [96.7, 755.0], [96.8, 773.0], [96.9, 775.0], [97.0, 777.0], [97.1, 782.0], [97.2, 787.0], [97.3, 790.0], [97.4, 813.0], [97.5, 826.0], [97.6, 826.0], [97.7, 833.0], [97.8, 837.0], [97.9, 839.0], [98.0, 845.0], [98.1, 878.0], [98.2, 878.0], [98.3, 882.0], [98.4, 910.0], [98.5, 914.0], [98.6, 918.0], [98.7, 925.0], [98.8, 1009.0], [98.9, 1025.0], [99.0, 1034.0], [99.1, 1040.0], [99.2, 1057.0], [99.3, 1069.0], [99.4, 1074.0], [99.5, 1079.0], [99.6, 1081.0], [99.7, 1125.0], [99.8, 1139.0], [99.9, 1170.0], [100.0, 1170.0]], "isOverall": false, "label": "Projets", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 480.0, "series": [{"data": [[0.0, 186.0], [100.0, 17.0]], "isOverall": false, "label": "Health Check", "isController": false}, {"data": [[0.0, 460.0], [1100.0, 1.0], [300.0, 56.0], [600.0, 27.0], [700.0, 10.0], [100.0, 207.0], [200.0, 88.0], [400.0, 49.0], [800.0, 10.0], [900.0, 8.0], [500.0, 21.0], [1000.0, 5.0]], "isOverall": false, "label": "Tâches", "isController": false}, {"data": [[0.0, 417.0], [1100.0, 3.0], [300.0, 54.0], [600.0, 22.0], [700.0, 11.0], [100.0, 263.0], [200.0, 100.0], [400.0, 47.0], [800.0, 6.0], [900.0, 9.0], [500.0, 23.0], [1000.0, 6.0]], "isOverall": false, "label": "Dashboard", "isController": false}, {"data": [[0.0, 480.0], [1100.0, 3.0], [300.0, 51.0], [600.0, 22.0], [700.0, 15.0], [100.0, 187.0], [200.0, 92.0], [400.0, 38.0], [800.0, 9.0], [900.0, 4.0], [500.0, 36.0], [1000.0, 9.0]], "isOverall": false, "label": "Projets", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 1100.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 258.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 2794.0, "series": [{"data": [[0.0, 2794.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [[1.0, 258.0]], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 1.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 3.0, "minX": 1.75066998E12, "maxY": 48.039189189189194, "series": [{"data": [[1.75067016E12, 25.0], [1.75067004E12, 19.790909090909096], [1.75067022E12, 25.0], [1.7506701E12, 25.0], [1.75067028E12, 22.999999999999996], [1.75066998E12, 3.0]], "isOverall": false, "label": "Phase 1 - Montée Douce (25 utilisateurs)", "isController": false}, {"data": [[1.75067034E12, 20.238970588235308], [1.75067052E12, 30.780040733197595], [1.7506704E12, 42.63036809815954], [1.75067058E12, 10.911111111111117], [1.75067028E12, 3.4], [1.75067046E12, 48.039189189189194]], "isOverall": false, "label": "Phase 2 - Charge Moyenne (50 utilisateurs)", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75067058E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 48.0, "minX": 1.0, "maxY": 545.25, "series": [{"data": [[9.0, 50.0], [10.0, 53.0], [11.0, 51.0], [12.0, 86.0], [13.0, 48.5], [14.0, 51.0], [15.0, 48.0], [1.0, 69.0], [17.0, 48.0], [18.0, 52.0], [19.0, 71.5], [20.0, 55.25], [23.0, 62.666666666666664], [24.0, 50.0], [25.0, 59.683908045977], [7.0, 49.0]], "isOverall": false, "label": "Health Check", "isController": false}, {"data": [[23.832512315270936, 59.261083743842356]], "isOverall": false, "label": "Health Check-Aggregated", "isController": false}, {"data": [[2.0, 65.0], [3.0, 64.0], [4.0, 63.0], [5.0, 90.0], [6.0, 70.0], [7.0, 61.666666666666664], [8.0, 65.75], [9.0, 83.66666666666667], [10.0, 71.2], [11.0, 63.333333333333336], [12.0, 77.375], [13.0, 70.60000000000001], [14.0, 73.33333333333333], [15.0, 70.8], [16.0, 76.42857142857143], [17.0, 78.3], [18.0, 75.42857142857143], [19.0, 71.69230769230771], [20.0, 107.77777777777777], [21.0, 77.66666666666666], [22.0, 103.54545454545456], [23.0, 95.0], [24.0, 94.0], [25.0, 82.54210526315788], [26.0, 117.0], [27.0, 96.6], [28.0, 74.25], [29.0, 153.2], [30.0, 99.35483870967741], [31.0, 85.74999999999999], [32.0, 90.1578947368421], [33.0, 88.33333333333333], [34.0, 105.45454545454544], [35.0, 159.23076923076923], [36.0, 137.04347826086956], [37.0, 116.26086956521738], [38.0, 167.79999999999998], [39.0, 202.54545454545456], [40.0, 145.4705882352941], [41.0, 144.88235294117646], [42.0, 106.08823529411764], [43.0, 257.5], [44.0, 545.25], [45.0, 341.95652173913044], [46.0, 210.0], [47.0, 453.8709677419355], [48.0, 257.2884615384615], [49.0, 395.16666666666663], [50.0, 391.4619565217394], [1.0, 66.0]], "isOverall": false, "label": "Tâches", "isController": false}, {"data": [[34.978768577494726, 195.06475583864096]], "isOverall": false, "label": "Tâches-Aggregated", "isController": false}, {"data": [[2.0, 120.0], [3.0, 110.33333333333333], [4.0, 91.66666666666667], [5.0, 95.2], [6.0, 84.66666666666667], [7.0, 85.75], [8.0, 95.33333333333333], [9.0, 86.33333333333333], [10.0, 82.75000000000001], [11.0, 79.0], [12.0, 77.0], [13.0, 94.75], [14.0, 87.57142857142857], [15.0, 86.5], [16.0, 98.39999999999999], [17.0, 85.25], [18.0, 76.5], [19.0, 91.5], [20.0, 102.00000000000001], [21.0, 97.39999999999999], [22.0, 113.5], [23.0, 107.99999999999999], [24.0, 104.77777777777779], [25.0, 90.52459016393442], [26.0, 110.0], [27.0, 101.5], [28.0, 90.35714285714286], [29.0, 111.07692307692308], [30.0, 117.6], [31.0, 86.66666666666666], [32.0, 90.82608695652173], [33.0, 93.3], [34.0, 104.16666666666667], [35.0, 136.84615384615384], [36.0, 106.84615384615384], [37.0, 154.45454545454547], [38.0, 126.19047619047619], [39.0, 229.81818181818184], [40.0, 171.33333333333334], [41.0, 180.46153846153845], [42.0, 132.38461538461536], [43.0, 276.25000000000006], [44.0, 310.3333333333333], [45.0, 382.40000000000003], [46.0, 183.16666666666666], [47.0, 419.65789473684214], [48.0, 242.57407407407402], [49.0, 390.27272727272725], [50.0, 407.28108108108097]], "isOverall": false, "label": "Dashboard", "isController": false}, {"data": [[34.60145681581682, 196.63891779396454]], "isOverall": false, "label": "Dashboard-Aggregated", "isController": false}, {"data": [[2.0, 76.0], [3.0, 62.0], [4.0, 71.66666666666667], [5.0, 78.5], [6.0, 54.0], [7.0, 77.0], [8.0, 66.5], [9.0, 62.5], [10.0, 70.8], [11.0, 78.0], [12.0, 70.5], [13.0, 84.59999999999998], [14.0, 74.0], [15.0, 93.0], [16.0, 81.91666666666667], [17.0, 94.66666666666666], [18.0, 79.19999999999999], [19.0, 71.29999999999998], [20.0, 99.125], [21.0, 94.0], [22.0, 85.55555555555557], [23.0, 75.78571428571428], [24.0, 89.33333333333333], [25.0, 75.81443298969073], [26.0, 100.83333333333333], [27.0, 83.57142857142858], [28.0, 79.625], [29.0, 99.16666666666667], [30.0, 100.92857142857142], [31.0, 88.0], [32.0, 72.72727272727273], [33.0, 94.5], [34.0, 89.25], [35.0, 133.53333333333333], [36.0, 158.66666666666669], [37.0, 126.06896551724138], [38.0, 133.9090909090909], [39.0, 234.36363636363637], [40.0, 179.46153846153845], [41.0, 168.875], [42.0, 122.47058823529412], [43.0, 207.7777777777778], [44.0, 267.2105263157895], [45.0, 351.4], [46.0, 120.875], [47.0, 524.9459459459459], [48.0, 245.42592592592592], [49.0, 351.64285714285717], [50.0, 416.8901098901102]], "isOverall": false, "label": "Projets", "isController": false}, {"data": [[34.96194503171252, 196.45348837209292]], "isOverall": false, "label": "Projets-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 50.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 1.95, "minX": 1.75066998E12, "maxY": 1118570.6166666667, "series": [{"data": [[1.75067016E12, 201539.55], [1.75067034E12, 399105.43333333335], [1.75067004E12, 111704.71666666666], [1.75067052E12, 746268.8666666667], [1.75067022E12, 210604.83333333334], [1.7506704E12, 977263.2333333333], [1.7506701E12, 200121.65], [1.75067058E12, 141970.01666666666], [1.75067028E12, 177733.43333333332], [1.75067046E12, 1118570.6166666667], [1.75066998E12, 1207.0166666666667]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.75067016E12, 692.6], [1.75067034E12, 1013.2], [1.75067004E12, 357.8], [1.75067052E12, 1925.75], [1.75067022E12, 719.7], [1.7506704E12, 2511.2833333333333], [1.7506701E12, 695.15], [1.75067058E12, 352.23333333333335], [1.75067028E12, 603.7833333333333], [1.75067046E12, 2910.3333333333335], [1.75066998E12, 1.95]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75067058E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 53.026315789473685, "minX": 1.75066998E12, "maxY": 390.32113821138216, "series": [{"data": [[1.75067016E12, 61.72093023255814], [1.75067004E12, 59.80645161290323], [1.75067022E12, 57.272727272727266], [1.7506701E12, 63.55319148936171], [1.75067028E12, 53.026315789473685]], "isOverall": false, "label": "Health Check", "isController": false}, {"data": [[1.75067016E12, 77.00000000000001], [1.75067034E12, 76.9873417721519], [1.75067004E12, 85.3888888888889], [1.75067052E12, 131.78947368421066], [1.75067022E12, 77.15217391304346], [1.7506704E12, 212.39150943396243], [1.7506701E12, 79.35555555555555], [1.75067058E12, 73.48648648648647], [1.75067028E12, 81.41025641025641], [1.75067046E12, 367.19920318725116]], "isOverall": false, "label": "Tâches", "isController": false}, {"data": [[1.75067016E12, 93.93181818181817], [1.75067034E12, 86.44554455445544], [1.75067004E12, 94.47222222222223], [1.75067052E12, 132.80891719745227], [1.75067022E12, 82.25], [1.7506704E12, 224.48430493273534], [1.7506701E12, 90.91111111111111], [1.75067058E12, 84.30434782608695], [1.75067028E12, 91.77500000000002], [1.75067046E12, 362.19753086419746], [1.75066998E12, 131.0]], "isOverall": false, "label": "Dashboard", "isController": false}, {"data": [[1.75067016E12, 78.06521739130434], [1.75067034E12, 80.44565217391303], [1.75067004E12, 78.83999999999997], [1.75067052E12, 117.7484662576688], [1.75067022E12, 74.5], [1.7506704E12, 212.1290322580645], [1.7506701E12, 73.85714285714286], [1.75067058E12, 72.80000000000001], [1.75067028E12, 75.00000000000001], [1.75067046E12, 390.32113821138216]], "isOverall": false, "label": "Projets", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75067058E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 18.55263157894737, "minX": 1.75066998E12, "maxY": 348.1707317073169, "series": [{"data": [[1.75067016E12, 26.674418604651166], [1.75067004E12, 22.064516129032256], [1.75067022E12, 22.79545454545454], [1.7506701E12, 28.44680851063829], [1.75067028E12, 18.55263157894737]], "isOverall": false, "label": "Health Check", "isController": false}, {"data": [[1.75067016E12, 43.97727272727273], [1.75067034E12, 44.24050632911394], [1.75067004E12, 50.611111111111114], [1.75067052E12, 91.09941520467838], [1.75067022E12, 43.869565217391305], [1.7506704E12, 173.18396226415084], [1.7506701E12, 45.022222222222226], [1.75067058E12, 34.837837837837846], [1.75067028E12, 48.025641025641015], [1.75067046E12, 324.8764940239042]], "isOverall": false, "label": "Tâches", "isController": false}, {"data": [[1.75067016E12, 58.50000000000001], [1.75067034E12, 52.42574257425743], [1.75067004E12, 58.277777777777786], [1.75067052E12, 92.46496815286623], [1.75067022E12, 47.25], [1.7506704E12, 185.72197309417044], [1.7506701E12, 55.46666666666666], [1.75067058E12, 46.60869565217391], [1.75067028E12, 55.22500000000001], [1.75067046E12, 320.73662551440333], [1.75066998E12, 92.0]], "isOverall": false, "label": "Dashboard", "isController": false}, {"data": [[1.75067016E12, 42.63043478260869], [1.75067034E12, 47.152173913043484], [1.75067004E12, 42.120000000000005], [1.75067052E12, 77.70552147239262], [1.75067022E12, 41.47826086956522], [1.7506704E12, 173.06451612903234], [1.7506701E12, 38.73809523809524], [1.75067058E12, 34.16666666666668], [1.75067028E12, 40.92307692307692], [1.75067046E12, 348.1707317073169]], "isOverall": false, "label": "Projets", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75067058E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.23566878980891723, "minX": 1.75066998E12, "maxY": 11.0, "series": [{"data": [[1.75067016E12, 0.5581395348837208], [1.75067004E12, 0.8064516129032259], [1.75067022E12, 0.4999999999999998], [1.7506701E12, 0.5957446808510639], [1.75067028E12, 0.5263157894736842]], "isOverall": false, "label": "Health Check", "isController": false}, {"data": [[1.75067016E12, 0.6136363636363636], [1.75067034E12, 0.48101265822784806], [1.75067004E12, 0.8333333333333333], [1.75067052E12, 0.36257309941520466], [1.75067022E12, 0.5652173913043479], [1.7506704E12, 0.33490566037735864], [1.7506701E12, 0.6444444444444444], [1.75067058E12, 0.3783783783783784], [1.75067028E12, 0.6153846153846153], [1.75067046E12, 0.31075697211155384]], "isOverall": false, "label": "Tâches", "isController": false}, {"data": [[1.75067016E12, 0.6363636363636362], [1.75067034E12, 0.34653465346534656], [1.75067004E12, 0.7777777777777776], [1.75067052E12, 0.23566878980891723], [1.75067022E12, 0.6249999999999999], [1.7506704E12, 0.3004484304932735], [1.7506701E12, 0.6888888888888891], [1.75067058E12, 0.39130434782608686], [1.75067028E12, 0.55], [1.75067046E12, 0.2798353909465021], [1.75066998E12, 11.0]], "isOverall": false, "label": "Dashboard", "isController": false}, {"data": [[1.75067016E12, 0.5869565217391304], [1.75067034E12, 0.3478260869565218], [1.75067004E12, 0.76], [1.75067052E12, 0.325153374233129], [1.75067022E12, 0.5217391304347827], [1.7506704E12, 0.29953917050691264], [1.7506701E12, 0.6904761904761902], [1.75067058E12, 0.33333333333333337], [1.75067028E12, 0.5897435897435898], [1.75067046E12, 0.31300813008130096]], "isOverall": false, "label": "Projets", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75067058E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 45.0, "minX": 1.75066998E12, "maxY": 1180.0, "series": [{"data": [[1.75067016E12, 252.0], [1.75067034E12, 245.0], [1.75067004E12, 174.0], [1.75067052E12, 500.0], [1.75067022E12, 154.0], [1.7506704E12, 755.0], [1.7506701E12, 167.0], [1.75067058E12, 136.0], [1.75067028E12, 151.0], [1.75067046E12, 1180.0], [1.75066998E12, 131.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.75067016E12, 105.60000000000005], [1.75067034E12, 112.0], [1.75067004E12, 110.0], [1.75067052E12, 237.8], [1.75067022E12, 100.0], [1.7506704E12, 490.10000000000014], [1.7506701E12, 112.0], [1.75067058E12, 96.0], [1.75067028E12, 107.30000000000001], [1.75067046E12, 789.6999999999999], [1.75066998E12, 131.0]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.75067016E12, 218.45999999999995], [1.75067034E12, 193.69999999999982], [1.75067004E12, 170.81], [1.75067052E12, 386.5599999999999], [1.75067022E12, 149.75000000000003], [1.7506704E12, 672.8700000000006], [1.7506701E12, 158.9999999999999], [1.75067058E12, 136.0], [1.75067028E12, 148.15000000000003], [1.75067046E12, 1102.3100000000004], [1.75066998E12, 131.0]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.75067016E12, 127.1], [1.75067034E12, 144.69999999999993], [1.75067004E12, 130.39999999999986], [1.75067052E12, 292.19999999999993], [1.75067022E12, 110.25], [1.7506704E12, 556.7500000000001], [1.7506701E12, 129.0], [1.75067058E12, 105.20000000000005], [1.75067028E12, 115.75000000000003], [1.75067046E12, 941.8999999999999], [1.75066998E12, 131.0]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.75067016E12, 46.0], [1.75067034E12, 52.0], [1.75067004E12, 46.0], [1.75067052E12, 58.0], [1.75067022E12, 46.0], [1.7506704E12, 53.0], [1.7506701E12, 45.0], [1.75067058E12, 56.0], [1.75067028E12, 45.0], [1.75067046E12, 57.0], [1.75066998E12, 131.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.75067016E12, 75.0], [1.75067034E12, 73.5], [1.75067004E12, 76.5], [1.75067052E12, 102.0], [1.75067022E12, 65.0], [1.7506704E12, 151.5], [1.7506701E12, 69.0], [1.75067058E12, 71.0], [1.75067028E12, 75.5], [1.75067046E12, 295.0], [1.75066998E12, 131.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75067058E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 70.5, "minX": 1.0, "maxY": 301.0, "series": [{"data": [[2.0, 70.5], [8.0, 87.5], [9.0, 89.0], [10.0, 102.5], [11.0, 179.0], [3.0, 71.0], [12.0, 201.5], [13.0, 282.0], [14.0, 301.0], [15.0, 233.0], [1.0, 75.0], [4.0, 71.5], [5.0, 71.0], [6.0, 77.5], [7.0, 75.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 15.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 33.0, "minX": 1.0, "maxY": 260.0, "series": [{"data": [[2.0, 35.5], [8.0, 48.5], [9.0, 51.5], [10.0, 66.0], [11.0, 140.0], [3.0, 33.0], [12.0, 158.5], [13.0, 238.0], [14.0, 260.0], [15.0, 194.0], [1.0, 39.5], [4.0, 35.0], [5.0, 34.0], [6.0, 39.5], [7.0, 39.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 15.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.75066998E12, "maxY": 12.25, "series": [{"data": [[1.75067016E12, 2.933333333333333], [1.75067034E12, 4.55], [1.75067004E12, 1.8333333333333333], [1.75067052E12, 8.183333333333334], [1.75067022E12, 3.066666666666667], [1.7506704E12, 10.933333333333334], [1.7506701E12, 3.0], [1.75067058E12, 1.5], [1.75067028E12, 2.6], [1.75067046E12, 12.25], [1.75066998E12, 0.016666666666666666]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75067058E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.75066998E12, "maxY": 12.333333333333334, "series": [{"data": [[1.75067016E12, 2.95], [1.75067034E12, 4.533333333333333], [1.75067004E12, 1.8333333333333333], [1.75067052E12, 8.183333333333334], [1.75067022E12, 3.066666666666667], [1.7506704E12, 10.866666666666667], [1.7506701E12, 2.9833333333333334], [1.75067058E12, 1.5], [1.75067028E12, 2.6], [1.75067046E12, 12.333333333333334], [1.75066998E12, 0.016666666666666666]], "isOverall": false, "label": "200", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75067058E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.75066998E12, "maxY": 4.183333333333334, "series": [{"data": [[1.75067016E12, 0.7666666666666667], [1.75067034E12, 1.5333333333333334], [1.75067004E12, 0.4166666666666667], [1.75067052E12, 2.716666666666667], [1.75067022E12, 0.7666666666666667], [1.7506704E12, 3.6166666666666667], [1.7506701E12, 0.7], [1.75067058E12, 0.5], [1.75067028E12, 0.65], [1.75067046E12, 4.1]], "isOverall": false, "label": "Projets-success", "isController": false}, {"data": [[1.75067016E12, 0.7333333333333333], [1.75067034E12, 1.6833333333333333], [1.75067004E12, 0.6], [1.75067052E12, 2.6166666666666667], [1.75067022E12, 0.8], [1.7506704E12, 3.716666666666667], [1.7506701E12, 0.75], [1.75067058E12, 0.38333333333333336], [1.75067028E12, 0.6666666666666666], [1.75067046E12, 4.05], [1.75066998E12, 0.016666666666666666]], "isOverall": false, "label": "Dashboard-success", "isController": false}, {"data": [[1.75067016E12, 0.7166666666666667], [1.75067004E12, 0.5166666666666667], [1.75067022E12, 0.7333333333333333], [1.7506701E12, 0.7833333333333333], [1.75067028E12, 0.6333333333333333]], "isOverall": false, "label": "Health Check-success", "isController": false}, {"data": [[1.75067016E12, 0.7333333333333333], [1.75067034E12, 1.3166666666666667], [1.75067004E12, 0.3], [1.75067052E12, 2.85], [1.75067022E12, 0.7666666666666667], [1.7506704E12, 3.533333333333333], [1.7506701E12, 0.75], [1.75067058E12, 0.6166666666666667], [1.75067028E12, 0.65], [1.75067046E12, 4.183333333333334]], "isOverall": false, "label": "Tâches-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75067058E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.75066998E12, "maxY": 12.333333333333334, "series": [{"data": [[1.75067016E12, 2.95], [1.75067034E12, 4.533333333333333], [1.75067004E12, 1.8333333333333333], [1.75067052E12, 8.183333333333334], [1.75067022E12, 3.066666666666667], [1.7506704E12, 10.866666666666667], [1.7506701E12, 2.9833333333333334], [1.75067058E12, 1.5], [1.75067028E12, 2.6], [1.75067046E12, 12.333333333333334], [1.75066998E12, 0.016666666666666666]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75067058E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

