/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 48.0, "minX": 0.0, "maxY": 480.0, "series": [{"data": [[0.0, 57.0], [0.1, 57.0], [0.2, 57.0], [0.3, 57.0], [0.4, 57.0], [0.5, 57.0], [0.6, 57.0], [0.7, 57.0], [0.8, 57.0], [0.9, 57.0], [1.0, 57.0], [1.1, 57.0], [1.2, 57.0], [1.3, 57.0], [1.4, 57.0], [1.5, 57.0], [1.6, 57.0], [1.7, 57.0], [1.8, 57.0], [1.9, 57.0], [2.0, 57.0], [2.1, 57.0], [2.2, 57.0], [2.3, 57.0], [2.4, 57.0], [2.5, 57.0], [2.6, 57.0], [2.7, 57.0], [2.8, 57.0], [2.9, 57.0], [3.0, 57.0], [3.1, 57.0], [3.2, 57.0], [3.3, 57.0], [3.4, 59.0], [3.5, 59.0], [3.6, 59.0], [3.7, 59.0], [3.8, 59.0], [3.9, 59.0], [4.0, 59.0], [4.1, 59.0], [4.2, 59.0], [4.3, 59.0], [4.4, 59.0], [4.5, 59.0], [4.6, 59.0], [4.7, 59.0], [4.8, 59.0], [4.9, 59.0], [5.0, 59.0], [5.1, 59.0], [5.2, 59.0], [5.3, 59.0], [5.4, 59.0], [5.5, 59.0], [5.6, 59.0], [5.7, 59.0], [5.8, 59.0], [5.9, 59.0], [6.0, 59.0], [6.1, 59.0], [6.2, 59.0], [6.3, 59.0], [6.4, 59.0], [6.5, 59.0], [6.6, 59.0], [6.7, 59.0], [6.8, 59.0], [6.9, 59.0], [7.0, 59.0], [7.1, 59.0], [7.2, 59.0], [7.3, 59.0], [7.4, 59.0], [7.5, 59.0], [7.6, 59.0], [7.7, 59.0], [7.8, 59.0], [7.9, 59.0], [8.0, 59.0], [8.1, 59.0], [8.2, 59.0], [8.3, 59.0], [8.4, 59.0], [8.5, 59.0], [8.6, 59.0], [8.7, 59.0], [8.8, 59.0], [8.9, 59.0], [9.0, 59.0], [9.1, 59.0], [9.2, 59.0], [9.3, 59.0], [9.4, 59.0], [9.5, 59.0], [9.6, 59.0], [9.7, 59.0], [9.8, 59.0], [9.9, 59.0], [10.0, 60.0], [10.1, 60.0], [10.2, 60.0], [10.3, 60.0], [10.4, 60.0], [10.5, 60.0], [10.6, 60.0], [10.7, 60.0], [10.8, 60.0], [10.9, 60.0], [11.0, 60.0], [11.1, 60.0], [11.2, 60.0], [11.3, 60.0], [11.4, 60.0], [11.5, 60.0], [11.6, 60.0], [11.7, 60.0], [11.8, 60.0], [11.9, 60.0], [12.0, 60.0], [12.1, 60.0], [12.2, 60.0], [12.3, 60.0], [12.4, 60.0], [12.5, 60.0], [12.6, 60.0], [12.7, 60.0], [12.8, 60.0], [12.9, 60.0], [13.0, 60.0], [13.1, 60.0], [13.2, 60.0], [13.3, 60.0], [13.4, 62.0], [13.5, 62.0], [13.6, 62.0], [13.7, 62.0], [13.8, 62.0], [13.9, 62.0], [14.0, 62.0], [14.1, 62.0], [14.2, 62.0], [14.3, 62.0], [14.4, 62.0], [14.5, 62.0], [14.6, 62.0], [14.7, 62.0], [14.8, 62.0], [14.9, 62.0], [15.0, 62.0], [15.1, 62.0], [15.2, 62.0], [15.3, 62.0], [15.4, 62.0], [15.5, 62.0], [15.6, 62.0], [15.7, 62.0], [15.8, 62.0], [15.9, 62.0], [16.0, 62.0], [16.1, 62.0], [16.2, 62.0], [16.3, 62.0], [16.4, 62.0], [16.5, 62.0], [16.6, 62.0], [16.7, 63.0], [16.8, 63.0], [16.9, 63.0], [17.0, 63.0], [17.1, 63.0], [17.2, 63.0], [17.3, 63.0], [17.4, 63.0], [17.5, 63.0], [17.6, 63.0], [17.7, 63.0], [17.8, 63.0], [17.9, 63.0], [18.0, 63.0], [18.1, 63.0], [18.2, 63.0], [18.3, 63.0], [18.4, 63.0], [18.5, 63.0], [18.6, 63.0], [18.7, 63.0], [18.8, 63.0], [18.9, 63.0], [19.0, 63.0], [19.1, 63.0], [19.2, 63.0], [19.3, 63.0], [19.4, 63.0], [19.5, 63.0], [19.6, 63.0], [19.7, 63.0], [19.8, 63.0], [19.9, 63.0], [20.0, 63.0], [20.1, 63.0], [20.2, 63.0], [20.3, 63.0], [20.4, 63.0], [20.5, 63.0], [20.6, 63.0], [20.7, 63.0], [20.8, 63.0], [20.9, 63.0], [21.0, 63.0], [21.1, 63.0], [21.2, 63.0], [21.3, 63.0], [21.4, 63.0], [21.5, 63.0], [21.6, 63.0], [21.7, 63.0], [21.8, 63.0], [21.9, 63.0], [22.0, 63.0], [22.1, 63.0], [22.2, 63.0], [22.3, 63.0], [22.4, 63.0], [22.5, 63.0], [22.6, 63.0], [22.7, 63.0], [22.8, 63.0], [22.9, 63.0], [23.0, 63.0], [23.1, 63.0], [23.2, 63.0], [23.3, 63.0], [23.4, 64.0], [23.5, 64.0], [23.6, 64.0], [23.7, 64.0], [23.8, 64.0], [23.9, 64.0], [24.0, 64.0], [24.1, 64.0], [24.2, 64.0], [24.3, 64.0], [24.4, 64.0], [24.5, 64.0], [24.6, 64.0], [24.7, 64.0], [24.8, 64.0], [24.9, 64.0], [25.0, 64.0], [25.1, 64.0], [25.2, 64.0], [25.3, 64.0], [25.4, 64.0], [25.5, 64.0], [25.6, 64.0], [25.7, 64.0], [25.8, 64.0], [25.9, 64.0], [26.0, 64.0], [26.1, 64.0], [26.2, 64.0], [26.3, 64.0], [26.4, 64.0], [26.5, 64.0], [26.6, 64.0], [26.7, 64.0], [26.8, 64.0], [26.9, 64.0], [27.0, 64.0], [27.1, 64.0], [27.2, 64.0], [27.3, 64.0], [27.4, 64.0], [27.5, 64.0], [27.6, 64.0], [27.7, 64.0], [27.8, 64.0], [27.9, 64.0], [28.0, 64.0], [28.1, 64.0], [28.2, 64.0], [28.3, 64.0], [28.4, 64.0], [28.5, 64.0], [28.6, 64.0], [28.7, 64.0], [28.8, 64.0], [28.9, 64.0], [29.0, 64.0], [29.1, 64.0], [29.2, 64.0], [29.3, 64.0], [29.4, 64.0], [29.5, 64.0], [29.6, 64.0], [29.7, 64.0], [29.8, 64.0], [29.9, 64.0], [30.0, 64.0], [30.1, 65.0], [30.2, 65.0], [30.3, 65.0], [30.4, 65.0], [30.5, 65.0], [30.6, 65.0], [30.7, 65.0], [30.8, 65.0], [30.9, 65.0], [31.0, 65.0], [31.1, 65.0], [31.2, 65.0], [31.3, 65.0], [31.4, 65.0], [31.5, 65.0], [31.6, 65.0], [31.7, 65.0], [31.8, 65.0], [31.9, 65.0], [32.0, 65.0], [32.1, 65.0], [32.2, 65.0], [32.3, 65.0], [32.4, 65.0], [32.5, 65.0], [32.6, 65.0], [32.7, 65.0], [32.8, 65.0], [32.9, 65.0], [33.0, 65.0], [33.1, 65.0], [33.2, 65.0], [33.3, 65.0], [33.4, 73.0], [33.5, 73.0], [33.6, 73.0], [33.7, 73.0], [33.8, 73.0], [33.9, 73.0], [34.0, 73.0], [34.1, 73.0], [34.2, 73.0], [34.3, 73.0], [34.4, 73.0], [34.5, 73.0], [34.6, 73.0], [34.7, 73.0], [34.8, 73.0], [34.9, 73.0], [35.0, 73.0], [35.1, 73.0], [35.2, 73.0], [35.3, 73.0], [35.4, 73.0], [35.5, 73.0], [35.6, 73.0], [35.7, 73.0], [35.8, 73.0], [35.9, 73.0], [36.0, 73.0], [36.1, 73.0], [36.2, 73.0], [36.3, 73.0], [36.4, 73.0], [36.5, 73.0], [36.6, 73.0], [36.7, 75.0], [36.8, 75.0], [36.9, 75.0], [37.0, 75.0], [37.1, 75.0], [37.2, 75.0], [37.3, 75.0], [37.4, 75.0], [37.5, 75.0], [37.6, 75.0], [37.7, 75.0], [37.8, 75.0], [37.9, 75.0], [38.0, 75.0], [38.1, 75.0], [38.2, 75.0], [38.3, 75.0], [38.4, 75.0], [38.5, 75.0], [38.6, 75.0], [38.7, 75.0], [38.8, 75.0], [38.9, 75.0], [39.0, 75.0], [39.1, 75.0], [39.2, 75.0], [39.3, 75.0], [39.4, 75.0], [39.5, 75.0], [39.6, 75.0], [39.7, 75.0], [39.8, 75.0], [39.9, 75.0], [40.0, 75.0], [40.1, 79.0], [40.2, 79.0], [40.3, 79.0], [40.4, 79.0], [40.5, 79.0], [40.6, 79.0], [40.7, 79.0], [40.8, 79.0], [40.9, 79.0], [41.0, 79.0], [41.1, 79.0], [41.2, 79.0], [41.3, 79.0], [41.4, 79.0], [41.5, 79.0], [41.6, 79.0], [41.7, 79.0], [41.8, 79.0], [41.9, 79.0], [42.0, 79.0], [42.1, 79.0], [42.2, 79.0], [42.3, 79.0], [42.4, 79.0], [42.5, 79.0], [42.6, 79.0], [42.7, 79.0], [42.8, 79.0], [42.9, 79.0], [43.0, 79.0], [43.1, 79.0], [43.2, 79.0], [43.3, 79.0], [43.4, 80.0], [43.5, 80.0], [43.6, 80.0], [43.7, 80.0], [43.8, 80.0], [43.9, 80.0], [44.0, 80.0], [44.1, 80.0], [44.2, 80.0], [44.3, 80.0], [44.4, 80.0], [44.5, 80.0], [44.6, 80.0], [44.7, 80.0], [44.8, 80.0], [44.9, 80.0], [45.0, 80.0], [45.1, 80.0], [45.2, 80.0], [45.3, 80.0], [45.4, 80.0], [45.5, 80.0], [45.6, 80.0], [45.7, 80.0], [45.8, 80.0], [45.9, 80.0], [46.0, 80.0], [46.1, 80.0], [46.2, 80.0], [46.3, 80.0], [46.4, 80.0], [46.5, 80.0], [46.6, 80.0], [46.7, 80.0], [46.8, 80.0], [46.9, 80.0], [47.0, 80.0], [47.1, 80.0], [47.2, 80.0], [47.3, 80.0], [47.4, 80.0], [47.5, 80.0], [47.6, 80.0], [47.7, 80.0], [47.8, 80.0], [47.9, 80.0], [48.0, 80.0], [48.1, 80.0], [48.2, 80.0], [48.3, 80.0], [48.4, 80.0], [48.5, 80.0], [48.6, 80.0], [48.7, 80.0], [48.8, 80.0], [48.9, 80.0], [49.0, 80.0], [49.1, 80.0], [49.2, 80.0], [49.3, 80.0], [49.4, 80.0], [49.5, 80.0], [49.6, 80.0], [49.7, 80.0], [49.8, 80.0], [49.9, 80.0], [50.0, 80.0], [50.1, 84.0], [50.2, 84.0], [50.3, 84.0], [50.4, 84.0], [50.5, 84.0], [50.6, 84.0], [50.7, 84.0], [50.8, 84.0], [50.9, 84.0], [51.0, 84.0], [51.1, 84.0], [51.2, 84.0], [51.3, 84.0], [51.4, 84.0], [51.5, 84.0], [51.6, 84.0], [51.7, 84.0], [51.8, 84.0], [51.9, 84.0], [52.0, 84.0], [52.1, 84.0], [52.2, 84.0], [52.3, 84.0], [52.4, 84.0], [52.5, 84.0], [52.6, 84.0], [52.7, 84.0], [52.8, 84.0], [52.9, 84.0], [53.0, 84.0], [53.1, 84.0], [53.2, 84.0], [53.3, 84.0], [53.4, 86.0], [53.5, 86.0], [53.6, 86.0], [53.7, 86.0], [53.8, 86.0], [53.9, 86.0], [54.0, 86.0], [54.1, 86.0], [54.2, 86.0], [54.3, 86.0], [54.4, 86.0], [54.5, 86.0], [54.6, 86.0], [54.7, 86.0], [54.8, 86.0], [54.9, 86.0], [55.0, 86.0], [55.1, 86.0], [55.2, 86.0], [55.3, 86.0], [55.4, 86.0], [55.5, 86.0], [55.6, 86.0], [55.7, 86.0], [55.8, 86.0], [55.9, 86.0], [56.0, 86.0], [56.1, 86.0], [56.2, 86.0], [56.3, 86.0], [56.4, 86.0], [56.5, 86.0], [56.6, 86.0], [56.7, 103.0], [56.8, 103.0], [56.9, 103.0], [57.0, 103.0], [57.1, 103.0], [57.2, 103.0], [57.3, 103.0], [57.4, 103.0], [57.5, 103.0], [57.6, 103.0], [57.7, 103.0], [57.8, 103.0], [57.9, 103.0], [58.0, 103.0], [58.1, 103.0], [58.2, 103.0], [58.3, 103.0], [58.4, 103.0], [58.5, 103.0], [58.6, 103.0], [58.7, 103.0], [58.8, 103.0], [58.9, 103.0], [59.0, 103.0], [59.1, 103.0], [59.2, 103.0], [59.3, 103.0], [59.4, 103.0], [59.5, 103.0], [59.6, 103.0], [59.7, 103.0], [59.8, 103.0], [59.9, 103.0], [60.0, 103.0], [60.1, 115.0], [60.2, 115.0], [60.3, 115.0], [60.4, 115.0], [60.5, 115.0], [60.6, 115.0], [60.7, 115.0], [60.8, 115.0], [60.9, 115.0], [61.0, 115.0], [61.1, 115.0], [61.2, 115.0], [61.3, 115.0], [61.4, 115.0], [61.5, 115.0], [61.6, 115.0], [61.7, 115.0], [61.8, 115.0], [61.9, 115.0], [62.0, 115.0], [62.1, 115.0], [62.2, 115.0], [62.3, 115.0], [62.4, 115.0], [62.5, 115.0], [62.6, 115.0], [62.7, 115.0], [62.8, 115.0], [62.9, 115.0], [63.0, 115.0], [63.1, 115.0], [63.2, 115.0], [63.3, 115.0], [63.4, 132.0], [63.5, 132.0], [63.6, 132.0], [63.7, 132.0], [63.8, 132.0], [63.9, 132.0], [64.0, 132.0], [64.1, 132.0], [64.2, 132.0], [64.3, 132.0], [64.4, 132.0], [64.5, 132.0], [64.6, 132.0], [64.7, 132.0], [64.8, 132.0], [64.9, 132.0], [65.0, 132.0], [65.1, 132.0], [65.2, 132.0], [65.3, 132.0], [65.4, 132.0], [65.5, 132.0], [65.6, 132.0], [65.7, 132.0], [65.8, 132.0], [65.9, 132.0], [66.0, 132.0], [66.1, 132.0], [66.2, 132.0], [66.3, 132.0], [66.4, 132.0], [66.5, 132.0], [66.6, 132.0], [66.7, 136.0], [66.8, 136.0], [66.9, 136.0], [67.0, 136.0], [67.1, 136.0], [67.2, 136.0], [67.3, 136.0], [67.4, 136.0], [67.5, 136.0], [67.6, 136.0], [67.7, 136.0], [67.8, 136.0], [67.9, 136.0], [68.0, 136.0], [68.1, 136.0], [68.2, 136.0], [68.3, 136.0], [68.4, 136.0], [68.5, 136.0], [68.6, 136.0], [68.7, 136.0], [68.8, 136.0], [68.9, 136.0], [69.0, 136.0], [69.1, 136.0], [69.2, 136.0], [69.3, 136.0], [69.4, 136.0], [69.5, 136.0], [69.6, 136.0], [69.7, 136.0], [69.8, 136.0], [69.9, 136.0], [70.0, 136.0], [70.1, 145.0], [70.2, 145.0], [70.3, 145.0], [70.4, 145.0], [70.5, 145.0], [70.6, 145.0], [70.7, 145.0], [70.8, 145.0], [70.9, 145.0], [71.0, 145.0], [71.1, 145.0], [71.2, 145.0], [71.3, 145.0], [71.4, 145.0], [71.5, 145.0], [71.6, 145.0], [71.7, 145.0], [71.8, 145.0], [71.9, 145.0], [72.0, 145.0], [72.1, 145.0], [72.2, 145.0], [72.3, 145.0], [72.4, 145.0], [72.5, 145.0], [72.6, 145.0], [72.7, 145.0], [72.8, 145.0], [72.9, 145.0], [73.0, 145.0], [73.1, 145.0], [73.2, 145.0], [73.3, 145.0], [73.4, 165.0], [73.5, 165.0], [73.6, 165.0], [73.7, 165.0], [73.8, 165.0], [73.9, 165.0], [74.0, 165.0], [74.1, 165.0], [74.2, 165.0], [74.3, 165.0], [74.4, 165.0], [74.5, 165.0], [74.6, 165.0], [74.7, 165.0], [74.8, 165.0], [74.9, 165.0], [75.0, 165.0], [75.1, 165.0], [75.2, 165.0], [75.3, 165.0], [75.4, 165.0], [75.5, 165.0], [75.6, 165.0], [75.7, 165.0], [75.8, 165.0], [75.9, 165.0], [76.0, 165.0], [76.1, 165.0], [76.2, 165.0], [76.3, 165.0], [76.4, 165.0], [76.5, 165.0], [76.6, 165.0], [76.7, 169.0], [76.8, 169.0], [76.9, 169.0], [77.0, 169.0], [77.1, 169.0], [77.2, 169.0], [77.3, 169.0], [77.4, 169.0], [77.5, 169.0], [77.6, 169.0], [77.7, 169.0], [77.8, 169.0], [77.9, 169.0], [78.0, 169.0], [78.1, 169.0], [78.2, 169.0], [78.3, 169.0], [78.4, 169.0], [78.5, 169.0], [78.6, 169.0], [78.7, 169.0], [78.8, 169.0], [78.9, 169.0], [79.0, 169.0], [79.1, 169.0], [79.2, 169.0], [79.3, 169.0], [79.4, 169.0], [79.5, 169.0], [79.6, 169.0], [79.7, 169.0], [79.8, 169.0], [79.9, 169.0], [80.0, 185.0], [80.1, 185.0], [80.2, 185.0], [80.3, 185.0], [80.4, 185.0], [80.5, 185.0], [80.6, 185.0], [80.7, 185.0], [80.8, 185.0], [80.9, 185.0], [81.0, 185.0], [81.1, 185.0], [81.2, 185.0], [81.3, 185.0], [81.4, 185.0], [81.5, 185.0], [81.6, 185.0], [81.7, 185.0], [81.8, 185.0], [81.9, 185.0], [82.0, 185.0], [82.1, 185.0], [82.2, 185.0], [82.3, 185.0], [82.4, 185.0], [82.5, 185.0], [82.6, 185.0], [82.7, 185.0], [82.8, 185.0], [82.9, 185.0], [83.0, 185.0], [83.1, 185.0], [83.2, 185.0], [83.3, 185.0], [83.4, 187.0], [83.5, 187.0], [83.6, 187.0], [83.7, 187.0], [83.8, 187.0], [83.9, 187.0], [84.0, 187.0], [84.1, 187.0], [84.2, 187.0], [84.3, 187.0], [84.4, 187.0], [84.5, 187.0], [84.6, 187.0], [84.7, 187.0], [84.8, 187.0], [84.9, 187.0], [85.0, 187.0], [85.1, 187.0], [85.2, 187.0], [85.3, 187.0], [85.4, 187.0], [85.5, 187.0], [85.6, 187.0], [85.7, 187.0], [85.8, 187.0], [85.9, 187.0], [86.0, 187.0], [86.1, 187.0], [86.2, 187.0], [86.3, 187.0], [86.4, 187.0], [86.5, 187.0], [86.6, 187.0], [86.7, 190.0], [86.8, 190.0], [86.9, 190.0], [87.0, 190.0], [87.1, 190.0], [87.2, 190.0], [87.3, 190.0], [87.4, 190.0], [87.5, 190.0], [87.6, 190.0], [87.7, 190.0], [87.8, 190.0], [87.9, 190.0], [88.0, 190.0], [88.1, 190.0], [88.2, 190.0], [88.3, 190.0], [88.4, 190.0], [88.5, 190.0], [88.6, 190.0], [88.7, 190.0], [88.8, 190.0], [88.9, 190.0], [89.0, 190.0], [89.1, 190.0], [89.2, 190.0], [89.3, 190.0], [89.4, 190.0], [89.5, 190.0], [89.6, 190.0], [89.7, 190.0], [89.8, 190.0], [89.9, 190.0], [90.0, 203.0], [90.1, 203.0], [90.2, 203.0], [90.3, 203.0], [90.4, 203.0], [90.5, 203.0], [90.6, 203.0], [90.7, 203.0], [90.8, 203.0], [90.9, 203.0], [91.0, 203.0], [91.1, 203.0], [91.2, 203.0], [91.3, 203.0], [91.4, 203.0], [91.5, 203.0], [91.6, 203.0], [91.7, 203.0], [91.8, 203.0], [91.9, 203.0], [92.0, 203.0], [92.1, 203.0], [92.2, 203.0], [92.3, 203.0], [92.4, 203.0], [92.5, 203.0], [92.6, 203.0], [92.7, 203.0], [92.8, 203.0], [92.9, 203.0], [93.0, 203.0], [93.1, 203.0], [93.2, 203.0], [93.3, 203.0], [93.4, 244.0], [93.5, 244.0], [93.6, 244.0], [93.7, 244.0], [93.8, 244.0], [93.9, 244.0], [94.0, 244.0], [94.1, 244.0], [94.2, 244.0], [94.3, 244.0], [94.4, 244.0], [94.5, 244.0], [94.6, 244.0], [94.7, 244.0], [94.8, 244.0], [94.9, 244.0], [95.0, 244.0], [95.1, 244.0], [95.2, 244.0], [95.3, 244.0], [95.4, 244.0], [95.5, 244.0], [95.6, 244.0], [95.7, 244.0], [95.8, 244.0], [95.9, 244.0], [96.0, 244.0], [96.1, 244.0], [96.2, 244.0], [96.3, 244.0], [96.4, 244.0], [96.5, 244.0], [96.6, 244.0], [96.7, 289.0], [96.8, 289.0], [96.9, 289.0], [97.0, 289.0], [97.1, 289.0], [97.2, 289.0], [97.3, 289.0], [97.4, 289.0], [97.5, 289.0], [97.6, 289.0], [97.7, 289.0], [97.8, 289.0], [97.9, 289.0], [98.0, 289.0], [98.1, 289.0], [98.2, 289.0], [98.3, 289.0], [98.4, 289.0], [98.5, 289.0], [98.6, 289.0], [98.7, 289.0], [98.8, 289.0], [98.9, 289.0], [99.0, 289.0], [99.1, 289.0], [99.2, 289.0], [99.3, 289.0], [99.4, 289.0], [99.5, 289.0], [99.6, 289.0], [99.7, 289.0], [99.8, 289.0], [99.9, 289.0]], "isOverall": false, "label": "03 - Liste Projets", "isController": false}, {"data": [[0.0, 56.0], [0.1, 56.0], [0.2, 56.0], [0.3, 56.0], [0.4, 57.0], [0.5, 57.0], [0.6, 57.0], [0.7, 57.0], [0.8, 57.0], [0.9, 57.0], [1.0, 57.0], [1.1, 57.0], [1.2, 57.0], [1.3, 58.0], [1.4, 58.0], [1.5, 58.0], [1.6, 58.0], [1.7, 58.0], [1.8, 58.0], [1.9, 58.0], [2.0, 58.0], [2.1, 58.0], [2.2, 58.0], [2.3, 58.0], [2.4, 58.0], [2.5, 58.0], [2.6, 58.0], [2.7, 58.0], [2.8, 58.0], [2.9, 58.0], [3.0, 58.0], [3.1, 58.0], [3.2, 58.0], [3.3, 58.0], [3.4, 58.0], [3.5, 58.0], [3.6, 58.0], [3.7, 58.0], [3.8, 58.0], [3.9, 58.0], [4.0, 59.0], [4.1, 59.0], [4.2, 59.0], [4.3, 59.0], [4.4, 59.0], [4.5, 59.0], [4.6, 59.0], [4.7, 59.0], [4.8, 59.0], [4.9, 59.0], [5.0, 59.0], [5.1, 59.0], [5.2, 59.0], [5.3, 59.0], [5.4, 59.0], [5.5, 59.0], [5.6, 59.0], [5.7, 59.0], [5.8, 59.0], [5.9, 59.0], [6.0, 59.0], [6.1, 59.0], [6.2, 59.0], [6.3, 59.0], [6.4, 60.0], [6.5, 60.0], [6.6, 60.0], [6.7, 60.0], [6.8, 60.0], [6.9, 60.0], [7.0, 60.0], [7.1, 60.0], [7.2, 60.0], [7.3, 60.0], [7.4, 60.0], [7.5, 60.0], [7.6, 60.0], [7.7, 60.0], [7.8, 60.0], [7.9, 60.0], [8.0, 60.0], [8.1, 60.0], [8.2, 60.0], [8.3, 60.0], [8.4, 60.0], [8.5, 60.0], [8.6, 60.0], [8.7, 60.0], [8.8, 60.0], [8.9, 60.0], [9.0, 60.0], [9.1, 60.0], [9.2, 61.0], [9.3, 61.0], [9.4, 61.0], [9.5, 61.0], [9.6, 61.0], [9.7, 61.0], [9.8, 61.0], [9.9, 61.0], [10.0, 61.0], [10.1, 61.0], [10.2, 61.0], [10.3, 61.0], [10.4, 61.0], [10.5, 61.0], [10.6, 61.0], [10.7, 61.0], [10.8, 61.0], [10.9, 61.0], [11.0, 61.0], [11.1, 61.0], [11.2, 61.0], [11.3, 61.0], [11.4, 61.0], [11.5, 61.0], [11.6, 61.0], [11.7, 61.0], [11.8, 61.0], [11.9, 61.0], [12.0, 61.0], [12.1, 61.0], [12.2, 61.0], [12.3, 61.0], [12.4, 61.0], [12.5, 61.0], [12.6, 61.0], [12.7, 61.0], [12.8, 61.0], [12.9, 61.0], [13.0, 61.0], [13.1, 61.0], [13.2, 61.0], [13.3, 61.0], [13.4, 61.0], [13.5, 61.0], [13.6, 61.0], [13.7, 61.0], [13.8, 61.0], [13.9, 61.0], [14.0, 61.0], [14.1, 61.0], [14.2, 61.0], [14.3, 61.0], [14.4, 61.0], [14.5, 61.0], [14.6, 61.0], [14.7, 61.0], [14.8, 62.0], [14.9, 62.0], [15.0, 62.0], [15.1, 62.0], [15.2, 62.0], [15.3, 62.0], [15.4, 62.0], [15.5, 62.0], [15.6, 62.0], [15.7, 62.0], [15.8, 62.0], [15.9, 62.0], [16.0, 62.0], [16.1, 62.0], [16.2, 62.0], [16.3, 62.0], [16.4, 62.0], [16.5, 62.0], [16.6, 62.0], [16.7, 62.0], [16.8, 62.0], [16.9, 62.0], [17.0, 62.0], [17.1, 62.0], [17.2, 62.0], [17.3, 62.0], [17.4, 62.0], [17.5, 62.0], [17.6, 62.0], [17.7, 62.0], [17.8, 62.0], [17.9, 62.0], [18.0, 62.0], [18.1, 62.0], [18.2, 62.0], [18.3, 62.0], [18.4, 62.0], [18.5, 62.0], [18.6, 62.0], [18.7, 62.0], [18.8, 62.0], [18.9, 62.0], [19.0, 62.0], [19.1, 62.0], [19.2, 62.0], [19.3, 62.0], [19.4, 62.0], [19.5, 62.0], [19.6, 62.0], [19.7, 62.0], [19.8, 62.0], [19.9, 62.0], [20.0, 63.0], [20.1, 63.0], [20.2, 63.0], [20.3, 63.0], [20.4, 63.0], [20.5, 63.0], [20.6, 63.0], [20.7, 63.0], [20.8, 63.0], [20.9, 63.0], [21.0, 63.0], [21.1, 63.0], [21.2, 63.0], [21.3, 63.0], [21.4, 63.0], [21.5, 63.0], [21.6, 63.0], [21.7, 63.0], [21.8, 63.0], [21.9, 63.0], [22.0, 63.0], [22.1, 63.0], [22.2, 63.0], [22.3, 63.0], [22.4, 63.0], [22.5, 63.0], [22.6, 63.0], [22.7, 63.0], [22.8, 63.0], [22.9, 63.0], [23.0, 63.0], [23.1, 63.0], [23.2, 63.0], [23.3, 63.0], [23.4, 63.0], [23.5, 63.0], [23.6, 63.0], [23.7, 63.0], [23.8, 63.0], [23.9, 63.0], [24.0, 63.0], [24.1, 63.0], [24.2, 63.0], [24.3, 63.0], [24.4, 63.0], [24.5, 63.0], [24.6, 63.0], [24.7, 63.0], [24.8, 63.0], [24.9, 63.0], [25.0, 63.0], [25.1, 63.0], [25.2, 63.0], [25.3, 63.0], [25.4, 63.0], [25.5, 63.0], [25.6, 63.0], [25.7, 63.0], [25.8, 63.0], [25.9, 63.0], [26.0, 63.0], [26.1, 63.0], [26.2, 63.0], [26.3, 63.0], [26.4, 64.0], [26.5, 64.0], [26.6, 64.0], [26.7, 64.0], [26.8, 64.0], [26.9, 64.0], [27.0, 64.0], [27.1, 64.0], [27.2, 64.0], [27.3, 64.0], [27.4, 64.0], [27.5, 64.0], [27.6, 64.0], [27.7, 64.0], [27.8, 64.0], [27.9, 64.0], [28.0, 64.0], [28.1, 64.0], [28.2, 64.0], [28.3, 64.0], [28.4, 64.0], [28.5, 64.0], [28.6, 64.0], [28.7, 64.0], [28.8, 64.0], [28.9, 64.0], [29.0, 64.0], [29.1, 64.0], [29.2, 64.0], [29.3, 64.0], [29.4, 64.0], [29.5, 64.0], [29.6, 65.0], [29.7, 65.0], [29.8, 65.0], [29.9, 65.0], [30.0, 65.0], [30.1, 65.0], [30.2, 65.0], [30.3, 65.0], [30.4, 65.0], [30.5, 65.0], [30.6, 65.0], [30.7, 65.0], [30.8, 65.0], [30.9, 65.0], [31.0, 65.0], [31.1, 65.0], [31.2, 65.0], [31.3, 65.0], [31.4, 65.0], [31.5, 65.0], [31.6, 65.0], [31.7, 65.0], [31.8, 65.0], [31.9, 65.0], [32.0, 65.0], [32.1, 65.0], [32.2, 65.0], [32.3, 65.0], [32.4, 65.0], [32.5, 65.0], [32.6, 65.0], [32.7, 65.0], [32.8, 65.0], [32.9, 65.0], [33.0, 65.0], [33.1, 65.0], [33.2, 66.0], [33.3, 66.0], [33.4, 66.0], [33.5, 66.0], [33.6, 66.0], [33.7, 66.0], [33.8, 66.0], [33.9, 66.0], [34.0, 66.0], [34.1, 66.0], [34.2, 66.0], [34.3, 66.0], [34.4, 67.0], [34.5, 67.0], [34.6, 67.0], [34.7, 67.0], [34.8, 67.0], [34.9, 67.0], [35.0, 67.0], [35.1, 67.0], [35.2, 67.0], [35.3, 67.0], [35.4, 67.0], [35.5, 67.0], [35.6, 67.0], [35.7, 67.0], [35.8, 67.0], [35.9, 67.0], [36.0, 67.0], [36.1, 67.0], [36.2, 67.0], [36.3, 67.0], [36.4, 67.0], [36.5, 67.0], [36.6, 67.0], [36.7, 67.0], [36.8, 68.0], [36.9, 68.0], [37.0, 68.0], [37.1, 68.0], [37.2, 68.0], [37.3, 68.0], [37.4, 68.0], [37.5, 68.0], [37.6, 68.0], [37.7, 68.0], [37.8, 68.0], [37.9, 68.0], [38.0, 68.0], [38.1, 68.0], [38.2, 68.0], [38.3, 68.0], [38.4, 68.0], [38.5, 68.0], [38.6, 68.0], [38.7, 68.0], [38.8, 69.0], [38.9, 69.0], [39.0, 69.0], [39.1, 69.0], [39.2, 69.0], [39.3, 69.0], [39.4, 69.0], [39.5, 69.0], [39.6, 69.0], [39.7, 69.0], [39.8, 69.0], [39.9, 69.0], [40.0, 70.0], [40.1, 70.0], [40.2, 70.0], [40.3, 70.0], [40.4, 70.0], [40.5, 70.0], [40.6, 70.0], [40.7, 70.0], [40.8, 70.0], [40.9, 70.0], [41.0, 70.0], [41.1, 70.0], [41.2, 70.0], [41.3, 70.0], [41.4, 70.0], [41.5, 70.0], [41.6, 71.0], [41.7, 71.0], [41.8, 71.0], [41.9, 71.0], [42.0, 71.0], [42.1, 71.0], [42.2, 71.0], [42.3, 71.0], [42.4, 71.0], [42.5, 71.0], [42.6, 71.0], [42.7, 71.0], [42.8, 71.0], [42.9, 71.0], [43.0, 71.0], [43.1, 71.0], [43.2, 71.0], [43.3, 71.0], [43.4, 71.0], [43.5, 71.0], [43.6, 71.0], [43.7, 71.0], [43.8, 71.0], [43.9, 71.0], [44.0, 71.0], [44.1, 71.0], [44.2, 71.0], [44.3, 71.0], [44.4, 71.0], [44.5, 71.0], [44.6, 71.0], [44.7, 71.0], [44.8, 71.0], [44.9, 71.0], [45.0, 71.0], [45.1, 71.0], [45.2, 72.0], [45.3, 72.0], [45.4, 72.0], [45.5, 72.0], [45.6, 72.0], [45.7, 72.0], [45.8, 72.0], [45.9, 72.0], [46.0, 72.0], [46.1, 72.0], [46.2, 72.0], [46.3, 72.0], [46.4, 72.0], [46.5, 72.0], [46.6, 72.0], [46.7, 72.0], [46.8, 72.0], [46.9, 73.0], [47.0, 73.0], [47.1, 73.0], [47.2, 73.0], [47.3, 73.0], [47.4, 73.0], [47.5, 73.0], [47.6, 73.0], [47.7, 73.0], [47.8, 73.0], [47.9, 73.0], [48.0, 73.0], [48.1, 77.0], [48.2, 77.0], [48.3, 77.0], [48.4, 77.0], [48.5, 77.0], [48.6, 77.0], [48.7, 77.0], [48.8, 77.0], [48.9, 80.0], [49.0, 80.0], [49.1, 80.0], [49.2, 80.0], [49.3, 80.0], [49.4, 80.0], [49.5, 80.0], [49.6, 80.0], [49.7, 80.0], [49.8, 80.0], [49.9, 80.0], [50.0, 80.0], [50.1, 81.0], [50.2, 81.0], [50.3, 81.0], [50.4, 81.0], [50.5, 82.0], [50.6, 82.0], [50.7, 82.0], [50.8, 82.0], [50.9, 83.0], [51.0, 83.0], [51.1, 83.0], [51.2, 83.0], [51.3, 83.0], [51.4, 83.0], [51.5, 83.0], [51.6, 83.0], [51.7, 83.0], [51.8, 83.0], [51.9, 83.0], [52.0, 83.0], [52.1, 83.0], [52.2, 83.0], [52.3, 83.0], [52.4, 83.0], [52.5, 83.0], [52.6, 83.0], [52.7, 83.0], [52.8, 83.0], [52.9, 83.0], [53.0, 83.0], [53.1, 83.0], [53.2, 84.0], [53.3, 84.0], [53.4, 84.0], [53.5, 84.0], [53.6, 85.0], [53.7, 85.0], [53.8, 85.0], [53.9, 85.0], [54.0, 85.0], [54.1, 85.0], [54.2, 85.0], [54.3, 85.0], [54.4, 87.0], [54.5, 87.0], [54.6, 87.0], [54.7, 87.0], [54.8, 87.0], [54.9, 87.0], [55.0, 87.0], [55.1, 87.0], [55.2, 87.0], [55.3, 87.0], [55.4, 87.0], [55.5, 87.0], [55.6, 87.0], [55.7, 87.0], [55.8, 87.0], [55.9, 87.0], [56.0, 88.0], [56.1, 88.0], [56.2, 88.0], [56.3, 88.0], [56.4, 89.0], [56.5, 89.0], [56.6, 89.0], [56.7, 89.0], [56.8, 89.0], [56.9, 89.0], [57.0, 89.0], [57.1, 89.0], [57.2, 89.0], [57.3, 89.0], [57.4, 89.0], [57.5, 89.0], [57.6, 89.0], [57.7, 89.0], [57.8, 89.0], [57.9, 89.0], [58.0, 89.0], [58.1, 89.0], [58.2, 89.0], [58.3, 89.0], [58.4, 89.0], [58.5, 89.0], [58.6, 89.0], [58.7, 89.0], [58.8, 90.0], [58.9, 90.0], [59.0, 90.0], [59.1, 90.0], [59.2, 91.0], [59.3, 91.0], [59.4, 91.0], [59.5, 91.0], [59.6, 91.0], [59.7, 91.0], [59.8, 91.0], [59.9, 91.0], [60.0, 92.0], [60.1, 92.0], [60.2, 92.0], [60.3, 92.0], [60.4, 93.0], [60.5, 93.0], [60.6, 93.0], [60.7, 93.0], [60.8, 94.0], [60.9, 94.0], [61.0, 94.0], [61.1, 94.0], [61.2, 94.0], [61.3, 94.0], [61.4, 94.0], [61.5, 94.0], [61.6, 95.0], [61.7, 95.0], [61.8, 95.0], [61.9, 95.0], [62.0, 95.0], [62.1, 95.0], [62.2, 95.0], [62.3, 95.0], [62.4, 96.0], [62.5, 96.0], [62.6, 96.0], [62.7, 96.0], [62.8, 96.0], [62.9, 96.0], [63.0, 96.0], [63.1, 96.0], [63.2, 96.0], [63.3, 96.0], [63.4, 96.0], [63.5, 96.0], [63.6, 97.0], [63.7, 97.0], [63.8, 97.0], [63.9, 97.0], [64.0, 100.0], [64.1, 100.0], [64.2, 100.0], [64.3, 100.0], [64.4, 101.0], [64.5, 101.0], [64.6, 101.0], [64.7, 101.0], [64.8, 102.0], [64.9, 102.0], [65.0, 102.0], [65.1, 102.0], [65.2, 102.0], [65.3, 102.0], [65.4, 102.0], [65.5, 102.0], [65.6, 104.0], [65.7, 104.0], [65.8, 104.0], [65.9, 104.0], [66.0, 105.0], [66.1, 105.0], [66.2, 105.0], [66.3, 105.0], [66.4, 106.0], [66.5, 106.0], [66.6, 106.0], [66.7, 106.0], [66.8, 106.0], [66.9, 108.0], [67.0, 108.0], [67.1, 108.0], [67.2, 108.0], [67.3, 108.0], [67.4, 108.0], [67.5, 108.0], [67.6, 108.0], [67.7, 108.0], [67.8, 108.0], [67.9, 108.0], [68.0, 108.0], [68.1, 109.0], [68.2, 109.0], [68.3, 109.0], [68.4, 109.0], [68.5, 110.0], [68.6, 110.0], [68.7, 110.0], [68.8, 110.0], [68.9, 113.0], [69.0, 113.0], [69.1, 113.0], [69.2, 113.0], [69.3, 113.0], [69.4, 113.0], [69.5, 113.0], [69.6, 113.0], [69.7, 114.0], [69.8, 114.0], [69.9, 114.0], [70.0, 114.0], [70.1, 114.0], [70.2, 114.0], [70.3, 114.0], [70.4, 114.0], [70.5, 115.0], [70.6, 115.0], [70.7, 115.0], [70.8, 115.0], [70.9, 116.0], [71.0, 116.0], [71.1, 116.0], [71.2, 116.0], [71.3, 116.0], [71.4, 116.0], [71.5, 116.0], [71.6, 116.0], [71.7, 116.0], [71.8, 116.0], [71.9, 116.0], [72.0, 116.0], [72.1, 117.0], [72.2, 117.0], [72.3, 117.0], [72.4, 117.0], [72.5, 118.0], [72.6, 118.0], [72.7, 118.0], [72.8, 118.0], [72.9, 119.0], [73.0, 119.0], [73.1, 119.0], [73.2, 119.0], [73.3, 120.0], [73.4, 120.0], [73.5, 120.0], [73.6, 120.0], [73.7, 121.0], [73.8, 121.0], [73.9, 121.0], [74.0, 121.0], [74.1, 121.0], [74.2, 121.0], [74.3, 121.0], [74.4, 121.0], [74.5, 121.0], [74.6, 121.0], [74.7, 121.0], [74.8, 121.0], [74.9, 125.0], [75.0, 125.0], [75.1, 125.0], [75.2, 125.0], [75.3, 128.0], [75.4, 128.0], [75.5, 128.0], [75.6, 128.0], [75.7, 131.0], [75.8, 131.0], [75.9, 131.0], [76.0, 131.0], [76.1, 133.0], [76.2, 133.0], [76.3, 133.0], [76.4, 133.0], [76.5, 133.0], [76.6, 133.0], [76.7, 133.0], [76.8, 133.0], [76.9, 133.0], [77.0, 133.0], [77.1, 133.0], [77.2, 133.0], [77.3, 133.0], [77.4, 133.0], [77.5, 133.0], [77.6, 133.0], [77.7, 133.0], [77.8, 133.0], [77.9, 133.0], [78.0, 133.0], [78.1, 135.0], [78.2, 135.0], [78.3, 135.0], [78.4, 135.0], [78.5, 135.0], [78.6, 135.0], [78.7, 135.0], [78.8, 135.0], [78.9, 136.0], [79.0, 136.0], [79.1, 136.0], [79.2, 136.0], [79.3, 138.0], [79.4, 138.0], [79.5, 138.0], [79.6, 138.0], [79.7, 138.0], [79.8, 138.0], [79.9, 138.0], [80.0, 138.0], [80.1, 140.0], [80.2, 140.0], [80.3, 140.0], [80.4, 140.0], [80.5, 142.0], [80.6, 142.0], [80.7, 142.0], [80.8, 142.0], [80.9, 143.0], [81.0, 143.0], [81.1, 143.0], [81.2, 143.0], [81.3, 145.0], [81.4, 145.0], [81.5, 145.0], [81.6, 145.0], [81.7, 145.0], [81.8, 145.0], [81.9, 145.0], [82.0, 145.0], [82.1, 146.0], [82.2, 146.0], [82.3, 146.0], [82.4, 146.0], [82.5, 146.0], [82.6, 146.0], [82.7, 146.0], [82.8, 146.0], [82.9, 147.0], [83.0, 147.0], [83.1, 147.0], [83.2, 147.0], [83.3, 147.0], [83.4, 147.0], [83.5, 147.0], [83.6, 147.0], [83.7, 149.0], [83.8, 149.0], [83.9, 149.0], [84.0, 149.0], [84.1, 150.0], [84.2, 150.0], [84.3, 150.0], [84.4, 150.0], [84.5, 154.0], [84.6, 154.0], [84.7, 154.0], [84.8, 154.0], [84.9, 155.0], [85.0, 155.0], [85.1, 155.0], [85.2, 155.0], [85.3, 155.0], [85.4, 155.0], [85.5, 155.0], [85.6, 155.0], [85.7, 155.0], [85.8, 155.0], [85.9, 155.0], [86.0, 155.0], [86.1, 159.0], [86.2, 159.0], [86.3, 159.0], [86.4, 159.0], [86.5, 159.0], [86.6, 159.0], [86.7, 159.0], [86.8, 159.0], [86.9, 161.0], [87.0, 161.0], [87.1, 161.0], [87.2, 161.0], [87.3, 164.0], [87.4, 164.0], [87.5, 164.0], [87.6, 164.0], [87.7, 165.0], [87.8, 165.0], [87.9, 165.0], [88.0, 165.0], [88.1, 165.0], [88.2, 165.0], [88.3, 165.0], [88.4, 165.0], [88.5, 165.0], [88.6, 165.0], [88.7, 165.0], [88.8, 165.0], [88.9, 166.0], [89.0, 166.0], [89.1, 166.0], [89.2, 166.0], [89.3, 168.0], [89.4, 168.0], [89.5, 168.0], [89.6, 168.0], [89.7, 169.0], [89.8, 169.0], [89.9, 169.0], [90.0, 169.0], [90.1, 172.0], [90.2, 172.0], [90.3, 172.0], [90.4, 172.0], [90.5, 173.0], [90.6, 173.0], [90.7, 173.0], [90.8, 173.0], [90.9, 174.0], [91.0, 174.0], [91.1, 174.0], [91.2, 174.0], [91.3, 188.0], [91.4, 188.0], [91.5, 188.0], [91.6, 188.0], [91.7, 191.0], [91.8, 191.0], [91.9, 191.0], [92.0, 191.0], [92.1, 193.0], [92.2, 193.0], [92.3, 193.0], [92.4, 193.0], [92.5, 196.0], [92.6, 196.0], [92.7, 196.0], [92.8, 196.0], [92.9, 198.0], [93.0, 198.0], [93.1, 198.0], [93.2, 198.0], [93.3, 200.0], [93.4, 200.0], [93.5, 200.0], [93.6, 200.0], [93.7, 203.0], [93.8, 203.0], [93.9, 203.0], [94.0, 203.0], [94.1, 206.0], [94.2, 206.0], [94.3, 206.0], [94.4, 206.0], [94.5, 211.0], [94.6, 211.0], [94.7, 211.0], [94.8, 211.0], [94.9, 215.0], [95.0, 215.0], [95.1, 215.0], [95.2, 215.0], [95.3, 225.0], [95.4, 225.0], [95.5, 225.0], [95.6, 225.0], [95.7, 227.0], [95.8, 227.0], [95.9, 227.0], [96.0, 227.0], [96.1, 231.0], [96.2, 231.0], [96.3, 231.0], [96.4, 231.0], [96.5, 239.0], [96.6, 239.0], [96.7, 239.0], [96.8, 239.0], [96.9, 239.0], [97.0, 239.0], [97.1, 239.0], [97.2, 239.0], [97.3, 240.0], [97.4, 240.0], [97.5, 240.0], [97.6, 240.0], [97.7, 246.0], [97.8, 246.0], [97.9, 246.0], [98.0, 246.0], [98.1, 271.0], [98.2, 271.0], [98.3, 271.0], [98.4, 271.0], [98.5, 277.0], [98.6, 277.0], [98.7, 277.0], [98.8, 277.0], [98.9, 289.0], [99.0, 289.0], [99.1, 289.0], [99.2, 289.0], [99.3, 435.0], [99.4, 435.0], [99.5, 435.0], [99.6, 435.0], [99.7, 480.0], [99.8, 480.0], [99.9, 480.0], [100.0, 480.0]], "isOverall": false, "label": "03 - Tâches", "isController": false}, {"data": [[0.0, 48.0], [0.1, 48.0], [0.2, 48.0], [0.3, 48.0], [0.4, 48.0], [0.5, 48.0], [0.6, 48.0], [0.7, 48.0], [0.8, 48.0], [0.9, 48.0], [1.0, 48.0], [1.1, 48.0], [1.2, 48.0], [1.3, 48.0], [1.4, 48.0], [1.5, 48.0], [1.6, 48.0], [1.7, 48.0], [1.8, 48.0], [1.9, 48.0], [2.0, 48.0], [2.1, 48.0], [2.2, 48.0], [2.3, 48.0], [2.4, 48.0], [2.5, 48.0], [2.6, 48.0], [2.7, 48.0], [2.8, 48.0], [2.9, 48.0], [3.0, 48.0], [3.1, 48.0], [3.2, 48.0], [3.3, 48.0], [3.4, 49.0], [3.5, 49.0], [3.6, 49.0], [3.7, 49.0], [3.8, 49.0], [3.9, 49.0], [4.0, 49.0], [4.1, 49.0], [4.2, 49.0], [4.3, 49.0], [4.4, 49.0], [4.5, 49.0], [4.6, 49.0], [4.7, 49.0], [4.8, 49.0], [4.9, 49.0], [5.0, 49.0], [5.1, 49.0], [5.2, 49.0], [5.3, 49.0], [5.4, 49.0], [5.5, 49.0], [5.6, 49.0], [5.7, 49.0], [5.8, 49.0], [5.9, 49.0], [6.0, 49.0], [6.1, 49.0], [6.2, 49.0], [6.3, 49.0], [6.4, 49.0], [6.5, 49.0], [6.6, 49.0], [6.7, 49.0], [6.8, 49.0], [6.9, 49.0], [7.0, 49.0], [7.1, 49.0], [7.2, 49.0], [7.3, 49.0], [7.4, 49.0], [7.5, 49.0], [7.6, 49.0], [7.7, 49.0], [7.8, 49.0], [7.9, 49.0], [8.0, 49.0], [8.1, 49.0], [8.2, 49.0], [8.3, 49.0], [8.4, 49.0], [8.5, 49.0], [8.6, 49.0], [8.7, 49.0], [8.8, 49.0], [8.9, 49.0], [9.0, 49.0], [9.1, 49.0], [9.2, 49.0], [9.3, 49.0], [9.4, 49.0], [9.5, 49.0], [9.6, 49.0], [9.7, 49.0], [9.8, 49.0], [9.9, 49.0], [10.0, 49.0], [10.1, 49.0], [10.2, 49.0], [10.3, 49.0], [10.4, 49.0], [10.5, 49.0], [10.6, 49.0], [10.7, 49.0], [10.8, 49.0], [10.9, 49.0], [11.0, 49.0], [11.1, 49.0], [11.2, 49.0], [11.3, 49.0], [11.4, 49.0], [11.5, 49.0], [11.6, 49.0], [11.7, 49.0], [11.8, 49.0], [11.9, 49.0], [12.0, 49.0], [12.1, 49.0], [12.2, 49.0], [12.3, 49.0], [12.4, 49.0], [12.5, 49.0], [12.6, 49.0], [12.7, 49.0], [12.8, 49.0], [12.9, 49.0], [13.0, 49.0], [13.1, 49.0], [13.2, 49.0], [13.3, 49.0], [13.4, 49.0], [13.5, 49.0], [13.6, 49.0], [13.7, 49.0], [13.8, 49.0], [13.9, 49.0], [14.0, 49.0], [14.1, 49.0], [14.2, 49.0], [14.3, 49.0], [14.4, 49.0], [14.5, 49.0], [14.6, 49.0], [14.7, 49.0], [14.8, 49.0], [14.9, 49.0], [15.0, 49.0], [15.1, 49.0], [15.2, 49.0], [15.3, 49.0], [15.4, 49.0], [15.5, 49.0], [15.6, 49.0], [15.7, 49.0], [15.8, 49.0], [15.9, 49.0], [16.0, 49.0], [16.1, 49.0], [16.2, 49.0], [16.3, 49.0], [16.4, 49.0], [16.5, 49.0], [16.6, 49.0], [16.7, 50.0], [16.8, 50.0], [16.9, 50.0], [17.0, 50.0], [17.1, 50.0], [17.2, 50.0], [17.3, 50.0], [17.4, 50.0], [17.5, 50.0], [17.6, 50.0], [17.7, 50.0], [17.8, 50.0], [17.9, 50.0], [18.0, 50.0], [18.1, 50.0], [18.2, 50.0], [18.3, 50.0], [18.4, 50.0], [18.5, 50.0], [18.6, 50.0], [18.7, 50.0], [18.8, 50.0], [18.9, 50.0], [19.0, 50.0], [19.1, 50.0], [19.2, 50.0], [19.3, 50.0], [19.4, 50.0], [19.5, 50.0], [19.6, 50.0], [19.7, 50.0], [19.8, 50.0], [19.9, 50.0], [20.0, 51.0], [20.1, 51.0], [20.2, 51.0], [20.3, 51.0], [20.4, 51.0], [20.5, 51.0], [20.6, 51.0], [20.7, 51.0], [20.8, 51.0], [20.9, 51.0], [21.0, 51.0], [21.1, 51.0], [21.2, 51.0], [21.3, 51.0], [21.4, 51.0], [21.5, 51.0], [21.6, 51.0], [21.7, 51.0], [21.8, 51.0], [21.9, 51.0], [22.0, 51.0], [22.1, 51.0], [22.2, 51.0], [22.3, 51.0], [22.4, 51.0], [22.5, 51.0], [22.6, 51.0], [22.7, 51.0], [22.8, 51.0], [22.9, 51.0], [23.0, 51.0], [23.1, 51.0], [23.2, 51.0], [23.3, 51.0], [23.4, 51.0], [23.5, 51.0], [23.6, 51.0], [23.7, 51.0], [23.8, 51.0], [23.9, 51.0], [24.0, 51.0], [24.1, 51.0], [24.2, 51.0], [24.3, 51.0], [24.4, 51.0], [24.5, 51.0], [24.6, 51.0], [24.7, 51.0], [24.8, 51.0], [24.9, 51.0], [25.0, 51.0], [25.1, 51.0], [25.2, 51.0], [25.3, 51.0], [25.4, 51.0], [25.5, 51.0], [25.6, 51.0], [25.7, 51.0], [25.8, 51.0], [25.9, 51.0], [26.0, 51.0], [26.1, 51.0], [26.2, 51.0], [26.3, 51.0], [26.4, 51.0], [26.5, 51.0], [26.6, 51.0], [26.7, 53.0], [26.8, 53.0], [26.9, 53.0], [27.0, 53.0], [27.1, 53.0], [27.2, 53.0], [27.3, 53.0], [27.4, 53.0], [27.5, 53.0], [27.6, 53.0], [27.7, 53.0], [27.8, 53.0], [27.9, 53.0], [28.0, 53.0], [28.1, 53.0], [28.2, 53.0], [28.3, 53.0], [28.4, 53.0], [28.5, 53.0], [28.6, 53.0], [28.7, 53.0], [28.8, 53.0], [28.9, 53.0], [29.0, 53.0], [29.1, 53.0], [29.2, 53.0], [29.3, 53.0], [29.4, 53.0], [29.5, 53.0], [29.6, 53.0], [29.7, 53.0], [29.8, 53.0], [29.9, 53.0], [30.0, 53.0], [30.1, 53.0], [30.2, 53.0], [30.3, 53.0], [30.4, 53.0], [30.5, 53.0], [30.6, 53.0], [30.7, 53.0], [30.8, 53.0], [30.9, 53.0], [31.0, 53.0], [31.1, 53.0], [31.2, 53.0], [31.3, 53.0], [31.4, 53.0], [31.5, 53.0], [31.6, 53.0], [31.7, 53.0], [31.8, 53.0], [31.9, 53.0], [32.0, 53.0], [32.1, 53.0], [32.2, 53.0], [32.3, 53.0], [32.4, 53.0], [32.5, 53.0], [32.6, 53.0], [32.7, 53.0], [32.8, 53.0], [32.9, 53.0], [33.0, 53.0], [33.1, 53.0], [33.2, 53.0], [33.3, 53.0], [33.4, 53.0], [33.5, 53.0], [33.6, 53.0], [33.7, 53.0], [33.8, 53.0], [33.9, 53.0], [34.0, 53.0], [34.1, 53.0], [34.2, 53.0], [34.3, 53.0], [34.4, 53.0], [34.5, 53.0], [34.6, 53.0], [34.7, 53.0], [34.8, 53.0], [34.9, 53.0], [35.0, 53.0], [35.1, 53.0], [35.2, 53.0], [35.3, 53.0], [35.4, 53.0], [35.5, 53.0], [35.6, 53.0], [35.7, 53.0], [35.8, 53.0], [35.9, 53.0], [36.0, 53.0], [36.1, 53.0], [36.2, 53.0], [36.3, 53.0], [36.4, 53.0], [36.5, 53.0], [36.6, 53.0], [36.7, 54.0], [36.8, 54.0], [36.9, 54.0], [37.0, 54.0], [37.1, 54.0], [37.2, 54.0], [37.3, 54.0], [37.4, 54.0], [37.5, 54.0], [37.6, 54.0], [37.7, 54.0], [37.8, 54.0], [37.9, 54.0], [38.0, 54.0], [38.1, 54.0], [38.2, 54.0], [38.3, 54.0], [38.4, 54.0], [38.5, 54.0], [38.6, 54.0], [38.7, 54.0], [38.8, 54.0], [38.9, 54.0], [39.0, 54.0], [39.1, 54.0], [39.2, 54.0], [39.3, 54.0], [39.4, 54.0], [39.5, 54.0], [39.6, 54.0], [39.7, 54.0], [39.8, 54.0], [39.9, 54.0], [40.0, 54.0], [40.1, 55.0], [40.2, 55.0], [40.3, 55.0], [40.4, 55.0], [40.5, 55.0], [40.6, 55.0], [40.7, 55.0], [40.8, 55.0], [40.9, 55.0], [41.0, 55.0], [41.1, 55.0], [41.2, 55.0], [41.3, 55.0], [41.4, 55.0], [41.5, 55.0], [41.6, 55.0], [41.7, 55.0], [41.8, 55.0], [41.9, 55.0], [42.0, 55.0], [42.1, 55.0], [42.2, 55.0], [42.3, 55.0], [42.4, 55.0], [42.5, 55.0], [42.6, 55.0], [42.7, 55.0], [42.8, 55.0], [42.9, 55.0], [43.0, 55.0], [43.1, 55.0], [43.2, 55.0], [43.3, 55.0], [43.4, 55.0], [43.5, 55.0], [43.6, 55.0], [43.7, 55.0], [43.8, 55.0], [43.9, 55.0], [44.0, 55.0], [44.1, 55.0], [44.2, 55.0], [44.3, 55.0], [44.4, 55.0], [44.5, 55.0], [44.6, 55.0], [44.7, 55.0], [44.8, 55.0], [44.9, 55.0], [45.0, 55.0], [45.1, 55.0], [45.2, 55.0], [45.3, 55.0], [45.4, 55.0], [45.5, 55.0], [45.6, 55.0], [45.7, 55.0], [45.8, 55.0], [45.9, 55.0], [46.0, 55.0], [46.1, 55.0], [46.2, 55.0], [46.3, 55.0], [46.4, 55.0], [46.5, 55.0], [46.6, 55.0], [46.7, 74.0], [46.8, 74.0], [46.9, 74.0], [47.0, 74.0], [47.1, 74.0], [47.2, 74.0], [47.3, 74.0], [47.4, 74.0], [47.5, 74.0], [47.6, 74.0], [47.7, 74.0], [47.8, 74.0], [47.9, 74.0], [48.0, 74.0], [48.1, 74.0], [48.2, 74.0], [48.3, 74.0], [48.4, 74.0], [48.5, 74.0], [48.6, 74.0], [48.7, 74.0], [48.8, 74.0], [48.9, 74.0], [49.0, 74.0], [49.1, 74.0], [49.2, 74.0], [49.3, 74.0], [49.4, 74.0], [49.5, 74.0], [49.6, 74.0], [49.7, 74.0], [49.8, 74.0], [49.9, 74.0], [50.0, 74.0], [50.1, 79.0], [50.2, 79.0], [50.3, 79.0], [50.4, 79.0], [50.5, 79.0], [50.6, 79.0], [50.7, 79.0], [50.8, 79.0], [50.9, 79.0], [51.0, 79.0], [51.1, 79.0], [51.2, 79.0], [51.3, 79.0], [51.4, 79.0], [51.5, 79.0], [51.6, 79.0], [51.7, 79.0], [51.8, 79.0], [51.9, 79.0], [52.0, 79.0], [52.1, 79.0], [52.2, 79.0], [52.3, 79.0], [52.4, 79.0], [52.5, 79.0], [52.6, 79.0], [52.7, 79.0], [52.8, 79.0], [52.9, 79.0], [53.0, 79.0], [53.1, 79.0], [53.2, 79.0], [53.3, 79.0], [53.4, 81.0], [53.5, 81.0], [53.6, 81.0], [53.7, 81.0], [53.8, 81.0], [53.9, 81.0], [54.0, 81.0], [54.1, 81.0], [54.2, 81.0], [54.3, 81.0], [54.4, 81.0], [54.5, 81.0], [54.6, 81.0], [54.7, 81.0], [54.8, 81.0], [54.9, 81.0], [55.0, 81.0], [55.1, 81.0], [55.2, 81.0], [55.3, 81.0], [55.4, 81.0], [55.5, 81.0], [55.6, 81.0], [55.7, 81.0], [55.8, 81.0], [55.9, 81.0], [56.0, 81.0], [56.1, 81.0], [56.2, 81.0], [56.3, 81.0], [56.4, 81.0], [56.5, 81.0], [56.6, 81.0], [56.7, 81.0], [56.8, 81.0], [56.9, 81.0], [57.0, 81.0], [57.1, 81.0], [57.2, 81.0], [57.3, 81.0], [57.4, 81.0], [57.5, 81.0], [57.6, 81.0], [57.7, 81.0], [57.8, 81.0], [57.9, 81.0], [58.0, 81.0], [58.1, 81.0], [58.2, 81.0], [58.3, 81.0], [58.4, 81.0], [58.5, 81.0], [58.6, 81.0], [58.7, 81.0], [58.8, 81.0], [58.9, 81.0], [59.0, 81.0], [59.1, 81.0], [59.2, 81.0], [59.3, 81.0], [59.4, 81.0], [59.5, 81.0], [59.6, 81.0], [59.7, 81.0], [59.8, 81.0], [59.9, 81.0], [60.0, 81.0], [60.1, 94.0], [60.2, 94.0], [60.3, 94.0], [60.4, 94.0], [60.5, 94.0], [60.6, 94.0], [60.7, 94.0], [60.8, 94.0], [60.9, 94.0], [61.0, 94.0], [61.1, 94.0], [61.2, 94.0], [61.3, 94.0], [61.4, 94.0], [61.5, 94.0], [61.6, 94.0], [61.7, 94.0], [61.8, 94.0], [61.9, 94.0], [62.0, 94.0], [62.1, 94.0], [62.2, 94.0], [62.3, 94.0], [62.4, 94.0], [62.5, 94.0], [62.6, 94.0], [62.7, 94.0], [62.8, 94.0], [62.9, 94.0], [63.0, 94.0], [63.1, 94.0], [63.2, 94.0], [63.3, 94.0], [63.4, 111.0], [63.5, 111.0], [63.6, 111.0], [63.7, 111.0], [63.8, 111.0], [63.9, 111.0], [64.0, 111.0], [64.1, 111.0], [64.2, 111.0], [64.3, 111.0], [64.4, 111.0], [64.5, 111.0], [64.6, 111.0], [64.7, 111.0], [64.8, 111.0], [64.9, 111.0], [65.0, 111.0], [65.1, 111.0], [65.2, 111.0], [65.3, 111.0], [65.4, 111.0], [65.5, 111.0], [65.6, 111.0], [65.7, 111.0], [65.8, 111.0], [65.9, 111.0], [66.0, 111.0], [66.1, 111.0], [66.2, 111.0], [66.3, 111.0], [66.4, 111.0], [66.5, 111.0], [66.6, 111.0], [66.7, 114.0], [66.8, 114.0], [66.9, 114.0], [67.0, 114.0], [67.1, 114.0], [67.2, 114.0], [67.3, 114.0], [67.4, 114.0], [67.5, 114.0], [67.6, 114.0], [67.7, 114.0], [67.8, 114.0], [67.9, 114.0], [68.0, 114.0], [68.1, 114.0], [68.2, 114.0], [68.3, 114.0], [68.4, 114.0], [68.5, 114.0], [68.6, 114.0], [68.7, 114.0], [68.8, 114.0], [68.9, 114.0], [69.0, 114.0], [69.1, 114.0], [69.2, 114.0], [69.3, 114.0], [69.4, 114.0], [69.5, 114.0], [69.6, 114.0], [69.7, 114.0], [69.8, 114.0], [69.9, 114.0], [70.0, 123.0], [70.1, 123.0], [70.2, 123.0], [70.3, 123.0], [70.4, 123.0], [70.5, 123.0], [70.6, 123.0], [70.7, 123.0], [70.8, 123.0], [70.9, 123.0], [71.0, 123.0], [71.1, 123.0], [71.2, 123.0], [71.3, 123.0], [71.4, 123.0], [71.5, 123.0], [71.6, 123.0], [71.7, 123.0], [71.8, 123.0], [71.9, 123.0], [72.0, 123.0], [72.1, 123.0], [72.2, 123.0], [72.3, 123.0], [72.4, 123.0], [72.5, 123.0], [72.6, 123.0], [72.7, 123.0], [72.8, 123.0], [72.9, 123.0], [73.0, 123.0], [73.1, 123.0], [73.2, 123.0], [73.3, 123.0], [73.4, 128.0], [73.5, 128.0], [73.6, 128.0], [73.7, 128.0], [73.8, 128.0], [73.9, 128.0], [74.0, 128.0], [74.1, 128.0], [74.2, 128.0], [74.3, 128.0], [74.4, 128.0], [74.5, 128.0], [74.6, 128.0], [74.7, 128.0], [74.8, 128.0], [74.9, 128.0], [75.0, 128.0], [75.1, 128.0], [75.2, 128.0], [75.3, 128.0], [75.4, 128.0], [75.5, 128.0], [75.6, 128.0], [75.7, 128.0], [75.8, 128.0], [75.9, 128.0], [76.0, 128.0], [76.1, 128.0], [76.2, 128.0], [76.3, 128.0], [76.4, 128.0], [76.5, 128.0], [76.6, 128.0], [76.7, 143.0], [76.8, 143.0], [76.9, 143.0], [77.0, 143.0], [77.1, 143.0], [77.2, 143.0], [77.3, 143.0], [77.4, 143.0], [77.5, 143.0], [77.6, 143.0], [77.7, 143.0], [77.8, 143.0], [77.9, 143.0], [78.0, 143.0], [78.1, 143.0], [78.2, 143.0], [78.3, 143.0], [78.4, 143.0], [78.5, 143.0], [78.6, 143.0], [78.7, 143.0], [78.8, 143.0], [78.9, 143.0], [79.0, 143.0], [79.1, 143.0], [79.2, 143.0], [79.3, 143.0], [79.4, 143.0], [79.5, 143.0], [79.6, 143.0], [79.7, 143.0], [79.8, 143.0], [79.9, 143.0], [80.0, 150.0], [80.1, 150.0], [80.2, 150.0], [80.3, 150.0], [80.4, 150.0], [80.5, 150.0], [80.6, 150.0], [80.7, 150.0], [80.8, 150.0], [80.9, 150.0], [81.0, 150.0], [81.1, 150.0], [81.2, 150.0], [81.3, 150.0], [81.4, 150.0], [81.5, 150.0], [81.6, 150.0], [81.7, 150.0], [81.8, 150.0], [81.9, 150.0], [82.0, 150.0], [82.1, 150.0], [82.2, 150.0], [82.3, 150.0], [82.4, 150.0], [82.5, 150.0], [82.6, 150.0], [82.7, 150.0], [82.8, 150.0], [82.9, 150.0], [83.0, 150.0], [83.1, 150.0], [83.2, 150.0], [83.3, 150.0], [83.4, 155.0], [83.5, 155.0], [83.6, 155.0], [83.7, 155.0], [83.8, 155.0], [83.9, 155.0], [84.0, 155.0], [84.1, 155.0], [84.2, 155.0], [84.3, 155.0], [84.4, 155.0], [84.5, 155.0], [84.6, 155.0], [84.7, 155.0], [84.8, 155.0], [84.9, 155.0], [85.0, 155.0], [85.1, 155.0], [85.2, 155.0], [85.3, 155.0], [85.4, 155.0], [85.5, 155.0], [85.6, 155.0], [85.7, 155.0], [85.8, 155.0], [85.9, 155.0], [86.0, 155.0], [86.1, 155.0], [86.2, 155.0], [86.3, 155.0], [86.4, 155.0], [86.5, 155.0], [86.6, 155.0], [86.7, 161.0], [86.8, 161.0], [86.9, 161.0], [87.0, 161.0], [87.1, 161.0], [87.2, 161.0], [87.3, 161.0], [87.4, 161.0], [87.5, 161.0], [87.6, 161.0], [87.7, 161.0], [87.8, 161.0], [87.9, 161.0], [88.0, 161.0], [88.1, 161.0], [88.2, 161.0], [88.3, 161.0], [88.4, 161.0], [88.5, 161.0], [88.6, 161.0], [88.7, 161.0], [88.8, 161.0], [88.9, 161.0], [89.0, 161.0], [89.1, 161.0], [89.2, 161.0], [89.3, 161.0], [89.4, 161.0], [89.5, 161.0], [89.6, 161.0], [89.7, 161.0], [89.8, 161.0], [89.9, 161.0], [90.0, 189.0], [90.1, 189.0], [90.2, 189.0], [90.3, 189.0], [90.4, 189.0], [90.5, 189.0], [90.6, 189.0], [90.7, 189.0], [90.8, 189.0], [90.9, 189.0], [91.0, 189.0], [91.1, 189.0], [91.2, 189.0], [91.3, 189.0], [91.4, 189.0], [91.5, 189.0], [91.6, 189.0], [91.7, 189.0], [91.8, 189.0], [91.9, 189.0], [92.0, 189.0], [92.1, 189.0], [92.2, 189.0], [92.3, 189.0], [92.4, 189.0], [92.5, 189.0], [92.6, 189.0], [92.7, 189.0], [92.8, 189.0], [92.9, 189.0], [93.0, 189.0], [93.1, 189.0], [93.2, 189.0], [93.3, 189.0], [93.4, 208.0], [93.5, 208.0], [93.6, 208.0], [93.7, 208.0], [93.8, 208.0], [93.9, 208.0], [94.0, 208.0], [94.1, 208.0], [94.2, 208.0], [94.3, 208.0], [94.4, 208.0], [94.5, 208.0], [94.6, 208.0], [94.7, 208.0], [94.8, 208.0], [94.9, 208.0], [95.0, 208.0], [95.1, 208.0], [95.2, 208.0], [95.3, 208.0], [95.4, 208.0], [95.5, 208.0], [95.6, 208.0], [95.7, 208.0], [95.8, 208.0], [95.9, 208.0], [96.0, 208.0], [96.1, 208.0], [96.2, 208.0], [96.3, 208.0], [96.4, 208.0], [96.5, 208.0], [96.6, 208.0], [96.7, 212.0], [96.8, 212.0], [96.9, 212.0], [97.0, 212.0], [97.1, 212.0], [97.2, 212.0], [97.3, 212.0], [97.4, 212.0], [97.5, 212.0], [97.6, 212.0], [97.7, 212.0], [97.8, 212.0], [97.9, 212.0], [98.0, 212.0], [98.1, 212.0], [98.2, 212.0], [98.3, 212.0], [98.4, 212.0], [98.5, 212.0], [98.6, 212.0], [98.7, 212.0], [98.8, 212.0], [98.9, 212.0], [99.0, 212.0], [99.1, 212.0], [99.2, 212.0], [99.3, 212.0], [99.4, 212.0], [99.5, 212.0], [99.6, 212.0], [99.7, 212.0], [99.8, 212.0], [99.9, 212.0]], "isOverall": false, "label": "02 - Health Check", "isController": false}, {"data": [[0.0, 63.0], [0.1, 63.0], [0.2, 63.0], [0.3, 63.0], [0.4, 63.0], [0.5, 63.0], [0.6, 63.0], [0.7, 63.0], [0.8, 63.0], [0.9, 63.0], [1.0, 63.0], [1.1, 63.0], [1.2, 63.0], [1.3, 63.0], [1.4, 63.0], [1.5, 63.0], [1.6, 63.0], [1.7, 63.0], [1.8, 63.0], [1.9, 63.0], [2.0, 63.0], [2.1, 63.0], [2.2, 63.0], [2.3, 63.0], [2.4, 63.0], [2.5, 63.0], [2.6, 63.0], [2.7, 63.0], [2.8, 63.0], [2.9, 63.0], [3.0, 63.0], [3.1, 63.0], [3.2, 63.0], [3.3, 63.0], [3.4, 64.0], [3.5, 64.0], [3.6, 64.0], [3.7, 64.0], [3.8, 64.0], [3.9, 64.0], [4.0, 64.0], [4.1, 64.0], [4.2, 64.0], [4.3, 64.0], [4.4, 64.0], [4.5, 64.0], [4.6, 64.0], [4.7, 64.0], [4.8, 64.0], [4.9, 64.0], [5.0, 64.0], [5.1, 64.0], [5.2, 64.0], [5.3, 64.0], [5.4, 64.0], [5.5, 64.0], [5.6, 64.0], [5.7, 64.0], [5.8, 64.0], [5.9, 64.0], [6.0, 64.0], [6.1, 64.0], [6.2, 64.0], [6.3, 64.0], [6.4, 64.0], [6.5, 64.0], [6.6, 64.0], [6.7, 64.0], [6.8, 64.0], [6.9, 64.0], [7.0, 64.0], [7.1, 64.0], [7.2, 64.0], [7.3, 64.0], [7.4, 64.0], [7.5, 64.0], [7.6, 64.0], [7.7, 64.0], [7.8, 64.0], [7.9, 64.0], [8.0, 64.0], [8.1, 64.0], [8.2, 64.0], [8.3, 64.0], [8.4, 64.0], [8.5, 64.0], [8.6, 64.0], [8.7, 64.0], [8.8, 64.0], [8.9, 64.0], [9.0, 64.0], [9.1, 64.0], [9.2, 64.0], [9.3, 64.0], [9.4, 64.0], [9.5, 64.0], [9.6, 64.0], [9.7, 64.0], [9.8, 64.0], [9.9, 64.0], [10.0, 64.0], [10.1, 64.0], [10.2, 64.0], [10.3, 64.0], [10.4, 64.0], [10.5, 64.0], [10.6, 64.0], [10.7, 64.0], [10.8, 64.0], [10.9, 64.0], [11.0, 64.0], [11.1, 64.0], [11.2, 64.0], [11.3, 64.0], [11.4, 64.0], [11.5, 64.0], [11.6, 64.0], [11.7, 64.0], [11.8, 64.0], [11.9, 64.0], [12.0, 64.0], [12.1, 64.0], [12.2, 64.0], [12.3, 64.0], [12.4, 64.0], [12.5, 64.0], [12.6, 64.0], [12.7, 64.0], [12.8, 64.0], [12.9, 64.0], [13.0, 64.0], [13.1, 64.0], [13.2, 64.0], [13.3, 64.0], [13.4, 66.0], [13.5, 66.0], [13.6, 66.0], [13.7, 66.0], [13.8, 66.0], [13.9, 66.0], [14.0, 66.0], [14.1, 66.0], [14.2, 66.0], [14.3, 66.0], [14.4, 66.0], [14.5, 66.0], [14.6, 66.0], [14.7, 66.0], [14.8, 66.0], [14.9, 66.0], [15.0, 66.0], [15.1, 66.0], [15.2, 66.0], [15.3, 66.0], [15.4, 66.0], [15.5, 66.0], [15.6, 66.0], [15.7, 66.0], [15.8, 66.0], [15.9, 66.0], [16.0, 66.0], [16.1, 66.0], [16.2, 66.0], [16.3, 66.0], [16.4, 66.0], [16.5, 66.0], [16.6, 66.0], [16.7, 68.0], [16.8, 68.0], [16.9, 68.0], [17.0, 68.0], [17.1, 68.0], [17.2, 68.0], [17.3, 68.0], [17.4, 68.0], [17.5, 68.0], [17.6, 68.0], [17.7, 68.0], [17.8, 68.0], [17.9, 68.0], [18.0, 68.0], [18.1, 68.0], [18.2, 68.0], [18.3, 68.0], [18.4, 68.0], [18.5, 68.0], [18.6, 68.0], [18.7, 68.0], [18.8, 68.0], [18.9, 68.0], [19.0, 68.0], [19.1, 68.0], [19.2, 68.0], [19.3, 68.0], [19.4, 68.0], [19.5, 68.0], [19.6, 68.0], [19.7, 68.0], [19.8, 68.0], [19.9, 68.0], [20.0, 72.0], [20.1, 72.0], [20.2, 72.0], [20.3, 72.0], [20.4, 72.0], [20.5, 72.0], [20.6, 72.0], [20.7, 72.0], [20.8, 72.0], [20.9, 72.0], [21.0, 72.0], [21.1, 72.0], [21.2, 72.0], [21.3, 72.0], [21.4, 72.0], [21.5, 72.0], [21.6, 72.0], [21.7, 72.0], [21.8, 72.0], [21.9, 72.0], [22.0, 72.0], [22.1, 72.0], [22.2, 72.0], [22.3, 72.0], [22.4, 72.0], [22.5, 72.0], [22.6, 72.0], [22.7, 72.0], [22.8, 72.0], [22.9, 72.0], [23.0, 72.0], [23.1, 72.0], [23.2, 72.0], [23.3, 72.0], [23.4, 77.0], [23.5, 77.0], [23.6, 77.0], [23.7, 77.0], [23.8, 77.0], [23.9, 77.0], [24.0, 77.0], [24.1, 77.0], [24.2, 77.0], [24.3, 77.0], [24.4, 77.0], [24.5, 77.0], [24.6, 77.0], [24.7, 77.0], [24.8, 77.0], [24.9, 77.0], [25.0, 77.0], [25.1, 77.0], [25.2, 77.0], [25.3, 77.0], [25.4, 77.0], [25.5, 77.0], [25.6, 77.0], [25.7, 77.0], [25.8, 77.0], [25.9, 77.0], [26.0, 77.0], [26.1, 77.0], [26.2, 77.0], [26.3, 77.0], [26.4, 77.0], [26.5, 77.0], [26.6, 77.0], [26.7, 86.0], [26.8, 86.0], [26.9, 86.0], [27.0, 86.0], [27.1, 86.0], [27.2, 86.0], [27.3, 86.0], [27.4, 86.0], [27.5, 86.0], [27.6, 86.0], [27.7, 86.0], [27.8, 86.0], [27.9, 86.0], [28.0, 86.0], [28.1, 86.0], [28.2, 86.0], [28.3, 86.0], [28.4, 86.0], [28.5, 86.0], [28.6, 86.0], [28.7, 86.0], [28.8, 86.0], [28.9, 86.0], [29.0, 86.0], [29.1, 86.0], [29.2, 86.0], [29.3, 86.0], [29.4, 86.0], [29.5, 86.0], [29.6, 86.0], [29.7, 86.0], [29.8, 86.0], [29.9, 86.0], [30.0, 92.0], [30.1, 92.0], [30.2, 92.0], [30.3, 92.0], [30.4, 92.0], [30.5, 92.0], [30.6, 92.0], [30.7, 92.0], [30.8, 92.0], [30.9, 92.0], [31.0, 92.0], [31.1, 92.0], [31.2, 92.0], [31.3, 92.0], [31.4, 92.0], [31.5, 92.0], [31.6, 92.0], [31.7, 92.0], [31.8, 92.0], [31.9, 92.0], [32.0, 92.0], [32.1, 92.0], [32.2, 92.0], [32.3, 92.0], [32.4, 92.0], [32.5, 92.0], [32.6, 92.0], [32.7, 92.0], [32.8, 92.0], [32.9, 92.0], [33.0, 92.0], [33.1, 92.0], [33.2, 92.0], [33.3, 92.0], [33.4, 92.0], [33.5, 92.0], [33.6, 92.0], [33.7, 92.0], [33.8, 92.0], [33.9, 92.0], [34.0, 92.0], [34.1, 92.0], [34.2, 92.0], [34.3, 92.0], [34.4, 92.0], [34.5, 92.0], [34.6, 92.0], [34.7, 92.0], [34.8, 92.0], [34.9, 92.0], [35.0, 92.0], [35.1, 92.0], [35.2, 92.0], [35.3, 92.0], [35.4, 92.0], [35.5, 92.0], [35.6, 92.0], [35.7, 92.0], [35.8, 92.0], [35.9, 92.0], [36.0, 92.0], [36.1, 92.0], [36.2, 92.0], [36.3, 92.0], [36.4, 92.0], [36.5, 92.0], [36.6, 92.0], [36.7, 94.0], [36.8, 94.0], [36.9, 94.0], [37.0, 94.0], [37.1, 94.0], [37.2, 94.0], [37.3, 94.0], [37.4, 94.0], [37.5, 94.0], [37.6, 94.0], [37.7, 94.0], [37.8, 94.0], [37.9, 94.0], [38.0, 94.0], [38.1, 94.0], [38.2, 94.0], [38.3, 94.0], [38.4, 94.0], [38.5, 94.0], [38.6, 94.0], [38.7, 94.0], [38.8, 94.0], [38.9, 94.0], [39.0, 94.0], [39.1, 94.0], [39.2, 94.0], [39.3, 94.0], [39.4, 94.0], [39.5, 94.0], [39.6, 94.0], [39.7, 94.0], [39.8, 94.0], [39.9, 94.0], [40.0, 94.0], [40.1, 94.0], [40.2, 94.0], [40.3, 94.0], [40.4, 94.0], [40.5, 94.0], [40.6, 94.0], [40.7, 94.0], [40.8, 94.0], [40.9, 94.0], [41.0, 94.0], [41.1, 94.0], [41.2, 94.0], [41.3, 94.0], [41.4, 94.0], [41.5, 94.0], [41.6, 94.0], [41.7, 94.0], [41.8, 94.0], [41.9, 94.0], [42.0, 94.0], [42.1, 94.0], [42.2, 94.0], [42.3, 94.0], [42.4, 94.0], [42.5, 94.0], [42.6, 94.0], [42.7, 94.0], [42.8, 94.0], [42.9, 94.0], [43.0, 94.0], [43.1, 94.0], [43.2, 94.0], [43.3, 94.0], [43.4, 97.0], [43.5, 97.0], [43.6, 97.0], [43.7, 97.0], [43.8, 97.0], [43.9, 97.0], [44.0, 97.0], [44.1, 97.0], [44.2, 97.0], [44.3, 97.0], [44.4, 97.0], [44.5, 97.0], [44.6, 97.0], [44.7, 97.0], [44.8, 97.0], [44.9, 97.0], [45.0, 97.0], [45.1, 97.0], [45.2, 97.0], [45.3, 97.0], [45.4, 97.0], [45.5, 97.0], [45.6, 97.0], [45.7, 97.0], [45.8, 97.0], [45.9, 97.0], [46.0, 97.0], [46.1, 97.0], [46.2, 97.0], [46.3, 97.0], [46.4, 97.0], [46.5, 97.0], [46.6, 97.0], [46.7, 97.0], [46.8, 97.0], [46.9, 97.0], [47.0, 97.0], [47.1, 97.0], [47.2, 97.0], [47.3, 97.0], [47.4, 97.0], [47.5, 97.0], [47.6, 97.0], [47.7, 97.0], [47.8, 97.0], [47.9, 97.0], [48.0, 97.0], [48.1, 97.0], [48.2, 97.0], [48.3, 97.0], [48.4, 97.0], [48.5, 97.0], [48.6, 97.0], [48.7, 97.0], [48.8, 97.0], [48.9, 97.0], [49.0, 97.0], [49.1, 97.0], [49.2, 97.0], [49.3, 97.0], [49.4, 97.0], [49.5, 97.0], [49.6, 97.0], [49.7, 97.0], [49.8, 97.0], [49.9, 97.0], [50.0, 98.0], [50.1, 98.0], [50.2, 98.0], [50.3, 98.0], [50.4, 98.0], [50.5, 98.0], [50.6, 98.0], [50.7, 98.0], [50.8, 98.0], [50.9, 98.0], [51.0, 98.0], [51.1, 98.0], [51.2, 98.0], [51.3, 98.0], [51.4, 98.0], [51.5, 98.0], [51.6, 98.0], [51.7, 98.0], [51.8, 98.0], [51.9, 98.0], [52.0, 98.0], [52.1, 98.0], [52.2, 98.0], [52.3, 98.0], [52.4, 98.0], [52.5, 98.0], [52.6, 98.0], [52.7, 98.0], [52.8, 98.0], [52.9, 98.0], [53.0, 98.0], [53.1, 98.0], [53.2, 98.0], [53.3, 98.0], [53.4, 101.0], [53.5, 101.0], [53.6, 101.0], [53.7, 101.0], [53.8, 101.0], [53.9, 101.0], [54.0, 101.0], [54.1, 101.0], [54.2, 101.0], [54.3, 101.0], [54.4, 101.0], [54.5, 101.0], [54.6, 101.0], [54.7, 101.0], [54.8, 101.0], [54.9, 101.0], [55.0, 101.0], [55.1, 101.0], [55.2, 101.0], [55.3, 101.0], [55.4, 101.0], [55.5, 101.0], [55.6, 101.0], [55.7, 101.0], [55.8, 101.0], [55.9, 101.0], [56.0, 101.0], [56.1, 101.0], [56.2, 101.0], [56.3, 101.0], [56.4, 101.0], [56.5, 101.0], [56.6, 101.0], [56.7, 101.0], [56.8, 101.0], [56.9, 101.0], [57.0, 101.0], [57.1, 101.0], [57.2, 101.0], [57.3, 101.0], [57.4, 101.0], [57.5, 101.0], [57.6, 101.0], [57.7, 101.0], [57.8, 101.0], [57.9, 101.0], [58.0, 101.0], [58.1, 101.0], [58.2, 101.0], [58.3, 101.0], [58.4, 101.0], [58.5, 101.0], [58.6, 101.0], [58.7, 101.0], [58.8, 101.0], [58.9, 101.0], [59.0, 101.0], [59.1, 101.0], [59.2, 101.0], [59.3, 101.0], [59.4, 101.0], [59.5, 101.0], [59.6, 101.0], [59.7, 101.0], [59.8, 101.0], [59.9, 101.0], [60.0, 102.0], [60.1, 102.0], [60.2, 102.0], [60.3, 102.0], [60.4, 102.0], [60.5, 102.0], [60.6, 102.0], [60.7, 102.0], [60.8, 102.0], [60.9, 102.0], [61.0, 102.0], [61.1, 102.0], [61.2, 102.0], [61.3, 102.0], [61.4, 102.0], [61.5, 102.0], [61.6, 102.0], [61.7, 102.0], [61.8, 102.0], [61.9, 102.0], [62.0, 102.0], [62.1, 102.0], [62.2, 102.0], [62.3, 102.0], [62.4, 102.0], [62.5, 102.0], [62.6, 102.0], [62.7, 102.0], [62.8, 102.0], [62.9, 102.0], [63.0, 102.0], [63.1, 102.0], [63.2, 102.0], [63.3, 102.0], [63.4, 102.0], [63.5, 102.0], [63.6, 102.0], [63.7, 102.0], [63.8, 102.0], [63.9, 102.0], [64.0, 102.0], [64.1, 102.0], [64.2, 102.0], [64.3, 102.0], [64.4, 102.0], [64.5, 102.0], [64.6, 102.0], [64.7, 102.0], [64.8, 102.0], [64.9, 102.0], [65.0, 102.0], [65.1, 102.0], [65.2, 102.0], [65.3, 102.0], [65.4, 102.0], [65.5, 102.0], [65.6, 102.0], [65.7, 102.0], [65.8, 102.0], [65.9, 102.0], [66.0, 102.0], [66.1, 102.0], [66.2, 102.0], [66.3, 102.0], [66.4, 102.0], [66.5, 102.0], [66.6, 102.0], [66.7, 102.0], [66.8, 102.0], [66.9, 102.0], [67.0, 102.0], [67.1, 102.0], [67.2, 102.0], [67.3, 102.0], [67.4, 102.0], [67.5, 102.0], [67.6, 102.0], [67.7, 102.0], [67.8, 102.0], [67.9, 102.0], [68.0, 102.0], [68.1, 102.0], [68.2, 102.0], [68.3, 102.0], [68.4, 102.0], [68.5, 102.0], [68.6, 102.0], [68.7, 102.0], [68.8, 102.0], [68.9, 102.0], [69.0, 102.0], [69.1, 102.0], [69.2, 102.0], [69.3, 102.0], [69.4, 102.0], [69.5, 102.0], [69.6, 102.0], [69.7, 102.0], [69.8, 102.0], [69.9, 102.0], [70.0, 107.0], [70.1, 107.0], [70.2, 107.0], [70.3, 107.0], [70.4, 107.0], [70.5, 107.0], [70.6, 107.0], [70.7, 107.0], [70.8, 107.0], [70.9, 107.0], [71.0, 107.0], [71.1, 107.0], [71.2, 107.0], [71.3, 107.0], [71.4, 107.0], [71.5, 107.0], [71.6, 107.0], [71.7, 107.0], [71.8, 107.0], [71.9, 107.0], [72.0, 107.0], [72.1, 107.0], [72.2, 107.0], [72.3, 107.0], [72.4, 107.0], [72.5, 107.0], [72.6, 107.0], [72.7, 107.0], [72.8, 107.0], [72.9, 107.0], [73.0, 107.0], [73.1, 107.0], [73.2, 107.0], [73.3, 107.0], [73.4, 123.0], [73.5, 123.0], [73.6, 123.0], [73.7, 123.0], [73.8, 123.0], [73.9, 123.0], [74.0, 123.0], [74.1, 123.0], [74.2, 123.0], [74.3, 123.0], [74.4, 123.0], [74.5, 123.0], [74.6, 123.0], [74.7, 123.0], [74.8, 123.0], [74.9, 123.0], [75.0, 123.0], [75.1, 123.0], [75.2, 123.0], [75.3, 123.0], [75.4, 123.0], [75.5, 123.0], [75.6, 123.0], [75.7, 123.0], [75.8, 123.0], [75.9, 123.0], [76.0, 123.0], [76.1, 123.0], [76.2, 123.0], [76.3, 123.0], [76.4, 123.0], [76.5, 123.0], [76.6, 123.0], [76.7, 134.0], [76.8, 134.0], [76.9, 134.0], [77.0, 134.0], [77.1, 134.0], [77.2, 134.0], [77.3, 134.0], [77.4, 134.0], [77.5, 134.0], [77.6, 134.0], [77.7, 134.0], [77.8, 134.0], [77.9, 134.0], [78.0, 134.0], [78.1, 134.0], [78.2, 134.0], [78.3, 134.0], [78.4, 134.0], [78.5, 134.0], [78.6, 134.0], [78.7, 134.0], [78.8, 134.0], [78.9, 134.0], [79.0, 134.0], [79.1, 134.0], [79.2, 134.0], [79.3, 134.0], [79.4, 134.0], [79.5, 134.0], [79.6, 134.0], [79.7, 134.0], [79.8, 134.0], [79.9, 134.0], [80.0, 135.0], [80.1, 135.0], [80.2, 135.0], [80.3, 135.0], [80.4, 135.0], [80.5, 135.0], [80.6, 135.0], [80.7, 135.0], [80.8, 135.0], [80.9, 135.0], [81.0, 135.0], [81.1, 135.0], [81.2, 135.0], [81.3, 135.0], [81.4, 135.0], [81.5, 135.0], [81.6, 135.0], [81.7, 135.0], [81.8, 135.0], [81.9, 135.0], [82.0, 135.0], [82.1, 135.0], [82.2, 135.0], [82.3, 135.0], [82.4, 135.0], [82.5, 135.0], [82.6, 135.0], [82.7, 135.0], [82.8, 135.0], [82.9, 135.0], [83.0, 135.0], [83.1, 135.0], [83.2, 135.0], [83.3, 135.0], [83.4, 143.0], [83.5, 143.0], [83.6, 143.0], [83.7, 143.0], [83.8, 143.0], [83.9, 143.0], [84.0, 143.0], [84.1, 143.0], [84.2, 143.0], [84.3, 143.0], [84.4, 143.0], [84.5, 143.0], [84.6, 143.0], [84.7, 143.0], [84.8, 143.0], [84.9, 143.0], [85.0, 143.0], [85.1, 143.0], [85.2, 143.0], [85.3, 143.0], [85.4, 143.0], [85.5, 143.0], [85.6, 143.0], [85.7, 143.0], [85.8, 143.0], [85.9, 143.0], [86.0, 143.0], [86.1, 143.0], [86.2, 143.0], [86.3, 143.0], [86.4, 143.0], [86.5, 143.0], [86.6, 143.0], [86.7, 150.0], [86.8, 150.0], [86.9, 150.0], [87.0, 150.0], [87.1, 150.0], [87.2, 150.0], [87.3, 150.0], [87.4, 150.0], [87.5, 150.0], [87.6, 150.0], [87.7, 150.0], [87.8, 150.0], [87.9, 150.0], [88.0, 150.0], [88.1, 150.0], [88.2, 150.0], [88.3, 150.0], [88.4, 150.0], [88.5, 150.0], [88.6, 150.0], [88.7, 150.0], [88.8, 150.0], [88.9, 150.0], [89.0, 150.0], [89.1, 150.0], [89.2, 150.0], [89.3, 150.0], [89.4, 150.0], [89.5, 150.0], [89.6, 150.0], [89.7, 150.0], [89.8, 150.0], [89.9, 150.0], [90.0, 152.0], [90.1, 152.0], [90.2, 152.0], [90.3, 152.0], [90.4, 152.0], [90.5, 152.0], [90.6, 152.0], [90.7, 152.0], [90.8, 152.0], [90.9, 152.0], [91.0, 152.0], [91.1, 152.0], [91.2, 152.0], [91.3, 152.0], [91.4, 152.0], [91.5, 152.0], [91.6, 152.0], [91.7, 152.0], [91.8, 152.0], [91.9, 152.0], [92.0, 152.0], [92.1, 152.0], [92.2, 152.0], [92.3, 152.0], [92.4, 152.0], [92.5, 152.0], [92.6, 152.0], [92.7, 152.0], [92.8, 152.0], [92.9, 152.0], [93.0, 152.0], [93.1, 152.0], [93.2, 152.0], [93.3, 152.0], [93.4, 231.0], [93.5, 231.0], [93.6, 231.0], [93.7, 231.0], [93.8, 231.0], [93.9, 231.0], [94.0, 231.0], [94.1, 231.0], [94.2, 231.0], [94.3, 231.0], [94.4, 231.0], [94.5, 231.0], [94.6, 231.0], [94.7, 231.0], [94.8, 231.0], [94.9, 231.0], [95.0, 231.0], [95.1, 231.0], [95.2, 231.0], [95.3, 231.0], [95.4, 231.0], [95.5, 231.0], [95.6, 231.0], [95.7, 231.0], [95.8, 231.0], [95.9, 231.0], [96.0, 231.0], [96.1, 231.0], [96.2, 231.0], [96.3, 231.0], [96.4, 231.0], [96.5, 231.0], [96.6, 231.0], [96.7, 259.0], [96.8, 259.0], [96.9, 259.0], [97.0, 259.0], [97.1, 259.0], [97.2, 259.0], [97.3, 259.0], [97.4, 259.0], [97.5, 259.0], [97.6, 259.0], [97.7, 259.0], [97.8, 259.0], [97.9, 259.0], [98.0, 259.0], [98.1, 259.0], [98.2, 259.0], [98.3, 259.0], [98.4, 259.0], [98.5, 259.0], [98.6, 259.0], [98.7, 259.0], [98.8, 259.0], [98.9, 259.0], [99.0, 259.0], [99.1, 259.0], [99.2, 259.0], [99.3, 259.0], [99.4, 259.0], [99.5, 259.0], [99.6, 259.0], [99.7, 259.0], [99.8, 259.0], [99.9, 259.0]], "isOverall": false, "label": "01 - Dashboard Principal", "isController": false}, {"data": [[0.0, 54.0], [0.1, 54.0], [0.2, 54.0], [0.3, 54.0], [0.4, 54.0], [0.5, 54.0], [0.6, 54.0], [0.7, 54.0], [0.8, 54.0], [0.9, 54.0], [1.0, 54.0], [1.1, 54.0], [1.2, 54.0], [1.3, 54.0], [1.4, 54.0], [1.5, 54.0], [1.6, 54.0], [1.7, 54.0], [1.8, 54.0], [1.9, 54.0], [2.0, 54.0], [2.1, 54.0], [2.2, 54.0], [2.3, 54.0], [2.4, 55.0], [2.5, 55.0], [2.6, 55.0], [2.7, 55.0], [2.8, 55.0], [2.9, 55.0], [3.0, 55.0], [3.1, 55.0], [3.2, 55.0], [3.3, 55.0], [3.4, 55.0], [3.5, 55.0], [3.6, 55.0], [3.7, 55.0], [3.8, 55.0], [3.9, 55.0], [4.0, 55.0], [4.1, 55.0], [4.2, 55.0], [4.3, 55.0], [4.4, 55.0], [4.5, 55.0], [4.6, 55.0], [4.7, 55.0], [4.8, 55.0], [4.9, 55.0], [5.0, 55.0], [5.1, 55.0], [5.2, 56.0], [5.3, 56.0], [5.4, 56.0], [5.5, 56.0], [5.6, 56.0], [5.7, 56.0], [5.8, 56.0], [5.9, 56.0], [6.0, 56.0], [6.1, 56.0], [6.2, 56.0], [6.3, 56.0], [6.4, 56.0], [6.5, 56.0], [6.6, 56.0], [6.7, 56.0], [6.8, 56.0], [6.9, 56.0], [7.0, 56.0], [7.1, 56.0], [7.2, 56.0], [7.3, 56.0], [7.4, 56.0], [7.5, 56.0], [7.6, 57.0], [7.7, 57.0], [7.8, 57.0], [7.9, 57.0], [8.0, 57.0], [8.1, 57.0], [8.2, 57.0], [8.3, 57.0], [8.4, 57.0], [8.5, 57.0], [8.6, 57.0], [8.7, 57.0], [8.8, 57.0], [8.9, 57.0], [9.0, 57.0], [9.1, 57.0], [9.2, 57.0], [9.3, 57.0], [9.4, 57.0], [9.5, 57.0], [9.6, 57.0], [9.7, 57.0], [9.8, 57.0], [9.9, 57.0], [10.0, 57.0], [10.1, 57.0], [10.2, 57.0], [10.3, 57.0], [10.4, 57.0], [10.5, 57.0], [10.6, 57.0], [10.7, 57.0], [10.8, 58.0], [10.9, 58.0], [11.0, 58.0], [11.1, 58.0], [11.2, 58.0], [11.3, 58.0], [11.4, 58.0], [11.5, 58.0], [11.6, 58.0], [11.7, 58.0], [11.8, 58.0], [11.9, 58.0], [12.0, 58.0], [12.1, 58.0], [12.2, 58.0], [12.3, 58.0], [12.4, 58.0], [12.5, 58.0], [12.6, 58.0], [12.7, 58.0], [12.8, 58.0], [12.9, 58.0], [13.0, 58.0], [13.1, 58.0], [13.2, 58.0], [13.3, 58.0], [13.4, 58.0], [13.5, 58.0], [13.6, 58.0], [13.7, 58.0], [13.8, 58.0], [13.9, 58.0], [14.0, 58.0], [14.1, 58.0], [14.2, 58.0], [14.3, 58.0], [14.4, 58.0], [14.5, 58.0], [14.6, 58.0], [14.7, 58.0], [14.8, 58.0], [14.9, 58.0], [15.0, 58.0], [15.1, 58.0], [15.2, 58.0], [15.3, 59.0], [15.4, 59.0], [15.5, 59.0], [15.6, 59.0], [15.7, 59.0], [15.8, 59.0], [15.9, 59.0], [16.0, 59.0], [16.1, 59.0], [16.2, 59.0], [16.3, 59.0], [16.4, 59.0], [16.5, 59.0], [16.6, 59.0], [16.7, 59.0], [16.8, 60.0], [16.9, 60.0], [17.0, 60.0], [17.1, 60.0], [17.2, 60.0], [17.3, 60.0], [17.4, 60.0], [17.5, 60.0], [17.6, 60.0], [17.7, 60.0], [17.8, 60.0], [17.9, 60.0], [18.0, 60.0], [18.1, 60.0], [18.2, 60.0], [18.3, 60.0], [18.4, 60.0], [18.5, 60.0], [18.6, 60.0], [18.7, 60.0], [18.8, 60.0], [18.9, 60.0], [19.0, 60.0], [19.1, 60.0], [19.2, 60.0], [19.3, 60.0], [19.4, 60.0], [19.5, 60.0], [19.6, 60.0], [19.7, 60.0], [19.8, 60.0], [19.9, 60.0], [20.0, 60.0], [20.1, 60.0], [20.2, 60.0], [20.3, 60.0], [20.4, 60.0], [20.5, 60.0], [20.6, 60.0], [20.7, 60.0], [20.8, 60.0], [20.9, 60.0], [21.0, 60.0], [21.1, 60.0], [21.2, 60.0], [21.3, 60.0], [21.4, 60.0], [21.5, 60.0], [21.6, 60.0], [21.7, 60.0], [21.8, 60.0], [21.9, 60.0], [22.0, 61.0], [22.1, 61.0], [22.2, 61.0], [22.3, 61.0], [22.4, 61.0], [22.5, 61.0], [22.6, 61.0], [22.7, 61.0], [22.8, 61.0], [22.9, 61.0], [23.0, 61.0], [23.1, 61.0], [23.2, 61.0], [23.3, 61.0], [23.4, 61.0], [23.5, 61.0], [23.6, 61.0], [23.7, 61.0], [23.8, 61.0], [23.9, 61.0], [24.0, 61.0], [24.1, 61.0], [24.2, 61.0], [24.3, 61.0], [24.4, 61.0], [24.5, 61.0], [24.6, 61.0], [24.7, 61.0], [24.8, 61.0], [24.9, 61.0], [25.0, 61.0], [25.1, 61.0], [25.2, 62.0], [25.3, 62.0], [25.4, 62.0], [25.5, 62.0], [25.6, 62.0], [25.7, 62.0], [25.8, 62.0], [25.9, 62.0], [26.0, 62.0], [26.1, 62.0], [26.2, 62.0], [26.3, 62.0], [26.4, 62.0], [26.5, 62.0], [26.6, 62.0], [26.7, 62.0], [26.8, 62.0], [26.9, 62.0], [27.0, 62.0], [27.1, 62.0], [27.2, 62.0], [27.3, 62.0], [27.4, 62.0], [27.5, 62.0], [27.6, 62.0], [27.7, 62.0], [27.8, 62.0], [27.9, 62.0], [28.0, 63.0], [28.1, 63.0], [28.2, 63.0], [28.3, 63.0], [28.4, 63.0], [28.5, 63.0], [28.6, 63.0], [28.7, 63.0], [28.8, 63.0], [28.9, 63.0], [29.0, 63.0], [29.1, 63.0], [29.2, 64.0], [29.3, 64.0], [29.4, 64.0], [29.5, 64.0], [29.6, 64.0], [29.7, 64.0], [29.8, 64.0], [29.9, 64.0], [30.0, 64.0], [30.1, 64.0], [30.2, 64.0], [30.3, 64.0], [30.4, 64.0], [30.5, 64.0], [30.6, 64.0], [30.7, 64.0], [30.8, 64.0], [30.9, 64.0], [31.0, 64.0], [31.1, 64.0], [31.2, 64.0], [31.3, 64.0], [31.4, 64.0], [31.5, 64.0], [31.6, 65.0], [31.7, 65.0], [31.8, 65.0], [31.9, 65.0], [32.0, 65.0], [32.1, 65.0], [32.2, 65.0], [32.3, 65.0], [32.4, 65.0], [32.5, 65.0], [32.6, 65.0], [32.7, 65.0], [32.8, 65.0], [32.9, 65.0], [33.0, 65.0], [33.1, 65.0], [33.2, 65.0], [33.3, 65.0], [33.4, 65.0], [33.5, 65.0], [33.6, 65.0], [33.7, 65.0], [33.8, 65.0], [33.9, 65.0], [34.0, 65.0], [34.1, 65.0], [34.2, 65.0], [34.3, 65.0], [34.4, 66.0], [34.5, 66.0], [34.6, 66.0], [34.7, 66.0], [34.8, 66.0], [34.9, 66.0], [35.0, 66.0], [35.1, 66.0], [35.2, 67.0], [35.3, 67.0], [35.4, 67.0], [35.5, 67.0], [35.6, 68.0], [35.7, 68.0], [35.8, 68.0], [35.9, 68.0], [36.0, 68.0], [36.1, 68.0], [36.2, 68.0], [36.3, 68.0], [36.4, 68.0], [36.5, 68.0], [36.6, 68.0], [36.7, 68.0], [36.8, 69.0], [36.9, 69.0], [37.0, 69.0], [37.1, 69.0], [37.2, 69.0], [37.3, 69.0], [37.4, 69.0], [37.5, 69.0], [37.6, 70.0], [37.7, 70.0], [37.8, 70.0], [37.9, 70.0], [38.0, 70.0], [38.1, 70.0], [38.2, 70.0], [38.3, 70.0], [38.4, 72.0], [38.5, 72.0], [38.6, 72.0], [38.7, 72.0], [38.8, 72.0], [38.9, 72.0], [39.0, 72.0], [39.1, 72.0], [39.2, 73.0], [39.3, 73.0], [39.4, 73.0], [39.5, 73.0], [39.6, 73.0], [39.7, 73.0], [39.8, 73.0], [39.9, 73.0], [40.0, 73.0], [40.1, 73.0], [40.2, 73.0], [40.3, 73.0], [40.4, 74.0], [40.5, 74.0], [40.6, 74.0], [40.7, 74.0], [40.8, 74.0], [40.9, 74.0], [41.0, 74.0], [41.1, 74.0], [41.2, 74.0], [41.3, 74.0], [41.4, 74.0], [41.5, 74.0], [41.6, 74.0], [41.7, 74.0], [41.8, 74.0], [41.9, 74.0], [42.0, 74.0], [42.1, 74.0], [42.2, 74.0], [42.3, 74.0], [42.4, 75.0], [42.5, 75.0], [42.6, 75.0], [42.7, 75.0], [42.8, 75.0], [42.9, 75.0], [43.0, 75.0], [43.1, 75.0], [43.2, 75.0], [43.3, 75.0], [43.4, 75.0], [43.5, 75.0], [43.6, 75.0], [43.7, 75.0], [43.8, 75.0], [43.9, 75.0], [44.0, 76.0], [44.1, 76.0], [44.2, 76.0], [44.3, 76.0], [44.4, 76.0], [44.5, 76.0], [44.6, 76.0], [44.7, 76.0], [44.8, 77.0], [44.9, 77.0], [45.0, 77.0], [45.1, 77.0], [45.2, 77.0], [45.3, 77.0], [45.4, 77.0], [45.5, 77.0], [45.6, 77.0], [45.7, 77.0], [45.8, 77.0], [45.9, 77.0], [46.0, 78.0], [46.1, 78.0], [46.2, 78.0], [46.3, 78.0], [46.4, 78.0], [46.5, 78.0], [46.6, 78.0], [46.7, 78.0], [46.8, 78.0], [46.9, 78.0], [47.0, 78.0], [47.1, 78.0], [47.2, 78.0], [47.3, 78.0], [47.4, 78.0], [47.5, 78.0], [47.6, 78.0], [47.7, 78.0], [47.8, 78.0], [47.9, 78.0], [48.0, 78.0], [48.1, 78.0], [48.2, 78.0], [48.3, 78.0], [48.4, 78.0], [48.5, 78.0], [48.6, 78.0], [48.7, 78.0], [48.8, 79.0], [48.9, 79.0], [49.0, 79.0], [49.1, 79.0], [49.2, 79.0], [49.3, 79.0], [49.4, 79.0], [49.5, 79.0], [49.6, 80.0], [49.7, 80.0], [49.8, 80.0], [49.9, 80.0], [50.0, 80.0], [50.1, 80.0], [50.2, 80.0], [50.3, 80.0], [50.4, 80.0], [50.5, 80.0], [50.6, 80.0], [50.7, 80.0], [50.8, 81.0], [50.9, 81.0], [51.0, 81.0], [51.1, 81.0], [51.2, 81.0], [51.3, 81.0], [51.4, 81.0], [51.5, 81.0], [51.6, 81.0], [51.7, 81.0], [51.8, 81.0], [51.9, 81.0], [52.0, 81.0], [52.1, 81.0], [52.2, 81.0], [52.3, 81.0], [52.4, 82.0], [52.5, 82.0], [52.6, 82.0], [52.7, 82.0], [52.8, 82.0], [52.9, 82.0], [53.0, 82.0], [53.1, 82.0], [53.2, 82.0], [53.3, 82.0], [53.4, 82.0], [53.5, 82.0], [53.6, 82.0], [53.7, 82.0], [53.8, 82.0], [53.9, 82.0], [54.0, 83.0], [54.1, 83.0], [54.2, 83.0], [54.3, 83.0], [54.4, 83.0], [54.5, 83.0], [54.6, 83.0], [54.7, 83.0], [54.8, 83.0], [54.9, 83.0], [55.0, 83.0], [55.1, 83.0], [55.2, 84.0], [55.3, 84.0], [55.4, 84.0], [55.5, 84.0], [55.6, 84.0], [55.7, 84.0], [55.8, 84.0], [55.9, 84.0], [56.0, 87.0], [56.1, 87.0], [56.2, 87.0], [56.3, 87.0], [56.4, 87.0], [56.5, 87.0], [56.6, 87.0], [56.7, 87.0], [56.8, 87.0], [56.9, 87.0], [57.0, 87.0], [57.1, 87.0], [57.2, 87.0], [57.3, 87.0], [57.4, 87.0], [57.5, 87.0], [57.6, 87.0], [57.7, 87.0], [57.8, 87.0], [57.9, 87.0], [58.0, 87.0], [58.1, 87.0], [58.2, 87.0], [58.3, 87.0], [58.4, 89.0], [58.5, 89.0], [58.6, 89.0], [58.7, 89.0], [58.8, 89.0], [58.9, 89.0], [59.0, 89.0], [59.1, 89.0], [59.2, 92.0], [59.3, 92.0], [59.4, 92.0], [59.5, 92.0], [59.6, 93.0], [59.7, 93.0], [59.8, 93.0], [59.9, 93.0], [60.0, 93.0], [60.1, 93.0], [60.2, 93.0], [60.3, 93.0], [60.4, 93.0], [60.5, 93.0], [60.6, 93.0], [60.7, 93.0], [60.8, 93.0], [60.9, 93.0], [61.0, 93.0], [61.1, 93.0], [61.2, 93.0], [61.3, 93.0], [61.4, 93.0], [61.5, 93.0], [61.6, 94.0], [61.7, 94.0], [61.8, 94.0], [61.9, 94.0], [62.0, 94.0], [62.1, 94.0], [62.2, 94.0], [62.3, 94.0], [62.4, 96.0], [62.5, 96.0], [62.6, 96.0], [62.7, 96.0], [62.8, 98.0], [62.9, 98.0], [63.0, 98.0], [63.1, 98.0], [63.2, 99.0], [63.3, 99.0], [63.4, 99.0], [63.5, 99.0], [63.6, 99.0], [63.7, 99.0], [63.8, 99.0], [63.9, 99.0], [64.0, 99.0], [64.1, 99.0], [64.2, 99.0], [64.3, 99.0], [64.4, 100.0], [64.5, 100.0], [64.6, 100.0], [64.7, 100.0], [64.8, 101.0], [64.9, 101.0], [65.0, 101.0], [65.1, 101.0], [65.2, 102.0], [65.3, 102.0], [65.4, 102.0], [65.5, 102.0], [65.6, 102.0], [65.7, 102.0], [65.8, 102.0], [65.9, 102.0], [66.0, 102.0], [66.1, 102.0], [66.2, 102.0], [66.3, 102.0], [66.4, 104.0], [66.5, 104.0], [66.6, 104.0], [66.7, 104.0], [66.8, 105.0], [66.9, 105.0], [67.0, 105.0], [67.1, 105.0], [67.2, 105.0], [67.3, 105.0], [67.4, 105.0], [67.5, 105.0], [67.6, 106.0], [67.7, 106.0], [67.8, 106.0], [67.9, 106.0], [68.0, 106.0], [68.1, 106.0], [68.2, 106.0], [68.3, 106.0], [68.4, 109.0], [68.5, 109.0], [68.6, 109.0], [68.7, 109.0], [68.8, 109.0], [68.9, 109.0], [69.0, 109.0], [69.1, 109.0], [69.2, 110.0], [69.3, 110.0], [69.4, 110.0], [69.5, 110.0], [69.6, 111.0], [69.7, 111.0], [69.8, 111.0], [69.9, 111.0], [70.0, 112.0], [70.1, 112.0], [70.2, 112.0], [70.3, 112.0], [70.4, 113.0], [70.5, 113.0], [70.6, 113.0], [70.7, 113.0], [70.8, 113.0], [70.9, 114.0], [71.0, 114.0], [71.1, 114.0], [71.2, 114.0], [71.3, 116.0], [71.4, 116.0], [71.5, 116.0], [71.6, 116.0], [71.7, 119.0], [71.8, 119.0], [71.9, 119.0], [72.0, 119.0], [72.1, 119.0], [72.2, 119.0], [72.3, 119.0], [72.4, 119.0], [72.5, 119.0], [72.6, 119.0], [72.7, 119.0], [72.8, 119.0], [72.9, 120.0], [73.0, 120.0], [73.1, 120.0], [73.2, 120.0], [73.3, 120.0], [73.4, 120.0], [73.5, 120.0], [73.6, 120.0], [73.7, 124.0], [73.8, 124.0], [73.9, 124.0], [74.0, 124.0], [74.1, 125.0], [74.2, 125.0], [74.3, 125.0], [74.4, 125.0], [74.5, 127.0], [74.6, 127.0], [74.7, 127.0], [74.8, 127.0], [74.9, 127.0], [75.0, 127.0], [75.1, 127.0], [75.2, 127.0], [75.3, 130.0], [75.4, 130.0], [75.5, 130.0], [75.6, 130.0], [75.7, 130.0], [75.8, 130.0], [75.9, 130.0], [76.0, 130.0], [76.1, 130.0], [76.2, 130.0], [76.3, 130.0], [76.4, 130.0], [76.5, 130.0], [76.6, 130.0], [76.7, 130.0], [76.8, 130.0], [76.9, 130.0], [77.0, 130.0], [77.1, 130.0], [77.2, 130.0], [77.3, 130.0], [77.4, 130.0], [77.5, 130.0], [77.6, 130.0], [77.7, 132.0], [77.8, 132.0], [77.9, 132.0], [78.0, 132.0], [78.1, 132.0], [78.2, 132.0], [78.3, 132.0], [78.4, 132.0], [78.5, 135.0], [78.6, 135.0], [78.7, 135.0], [78.8, 135.0], [78.9, 138.0], [79.0, 138.0], [79.1, 138.0], [79.2, 138.0], [79.3, 138.0], [79.4, 138.0], [79.5, 138.0], [79.6, 138.0], [79.7, 139.0], [79.8, 139.0], [79.9, 139.0], [80.0, 139.0], [80.1, 139.0], [80.2, 139.0], [80.3, 139.0], [80.4, 139.0], [80.5, 140.0], [80.6, 140.0], [80.7, 140.0], [80.8, 140.0], [80.9, 140.0], [81.0, 140.0], [81.1, 140.0], [81.2, 140.0], [81.3, 141.0], [81.4, 141.0], [81.5, 141.0], [81.6, 141.0], [81.7, 142.0], [81.8, 142.0], [81.9, 142.0], [82.0, 142.0], [82.1, 148.0], [82.2, 148.0], [82.3, 148.0], [82.4, 148.0], [82.5, 148.0], [82.6, 148.0], [82.7, 148.0], [82.8, 148.0], [82.9, 148.0], [83.0, 148.0], [83.1, 148.0], [83.2, 148.0], [83.3, 149.0], [83.4, 149.0], [83.5, 149.0], [83.6, 149.0], [83.7, 153.0], [83.8, 153.0], [83.9, 153.0], [84.0, 153.0], [84.1, 154.0], [84.2, 154.0], [84.3, 154.0], [84.4, 154.0], [84.5, 154.0], [84.6, 154.0], [84.7, 154.0], [84.8, 154.0], [84.9, 156.0], [85.0, 156.0], [85.1, 156.0], [85.2, 156.0], [85.3, 159.0], [85.4, 159.0], [85.5, 159.0], [85.6, 159.0], [85.7, 162.0], [85.8, 162.0], [85.9, 162.0], [86.0, 162.0], [86.1, 164.0], [86.2, 164.0], [86.3, 164.0], [86.4, 164.0], [86.5, 168.0], [86.6, 168.0], [86.7, 168.0], [86.8, 168.0], [86.9, 171.0], [87.0, 171.0], [87.1, 171.0], [87.2, 171.0], [87.3, 177.0], [87.4, 177.0], [87.5, 177.0], [87.6, 177.0], [87.7, 179.0], [87.8, 179.0], [87.9, 179.0], [88.0, 179.0], [88.1, 180.0], [88.2, 180.0], [88.3, 180.0], [88.4, 180.0], [88.5, 182.0], [88.6, 182.0], [88.7, 182.0], [88.8, 182.0], [88.9, 186.0], [89.0, 186.0], [89.1, 186.0], [89.2, 186.0], [89.3, 187.0], [89.4, 187.0], [89.5, 187.0], [89.6, 187.0], [89.7, 187.0], [89.8, 187.0], [89.9, 187.0], [90.0, 187.0], [90.1, 187.0], [90.2, 187.0], [90.3, 187.0], [90.4, 187.0], [90.5, 191.0], [90.6, 191.0], [90.7, 191.0], [90.8, 191.0], [90.9, 191.0], [91.0, 191.0], [91.1, 191.0], [91.2, 191.0], [91.3, 192.0], [91.4, 192.0], [91.5, 192.0], [91.6, 192.0], [91.7, 204.0], [91.8, 204.0], [91.9, 204.0], [92.0, 204.0], [92.1, 205.0], [92.2, 205.0], [92.3, 205.0], [92.4, 205.0], [92.5, 206.0], [92.6, 206.0], [92.7, 206.0], [92.8, 206.0], [92.9, 208.0], [93.0, 208.0], [93.1, 208.0], [93.2, 208.0], [93.3, 211.0], [93.4, 211.0], [93.5, 211.0], [93.6, 211.0], [93.7, 211.0], [93.8, 211.0], [93.9, 211.0], [94.0, 211.0], [94.1, 217.0], [94.2, 217.0], [94.3, 217.0], [94.4, 217.0], [94.5, 222.0], [94.6, 222.0], [94.7, 222.0], [94.8, 222.0], [94.9, 228.0], [95.0, 228.0], [95.1, 228.0], [95.2, 228.0], [95.3, 238.0], [95.4, 238.0], [95.5, 238.0], [95.6, 238.0], [95.7, 245.0], [95.8, 245.0], [95.9, 245.0], [96.0, 245.0], [96.1, 250.0], [96.2, 250.0], [96.3, 250.0], [96.4, 250.0], [96.5, 259.0], [96.6, 259.0], [96.7, 259.0], [96.8, 259.0], [96.9, 262.0], [97.0, 262.0], [97.1, 262.0], [97.2, 262.0], [97.3, 265.0], [97.4, 265.0], [97.5, 265.0], [97.6, 265.0], [97.7, 276.0], [97.8, 276.0], [97.9, 276.0], [98.0, 276.0], [98.1, 283.0], [98.2, 283.0], [98.3, 283.0], [98.4, 283.0], [98.5, 286.0], [98.6, 286.0], [98.7, 286.0], [98.8, 286.0], [98.9, 315.0], [99.0, 315.0], [99.1, 315.0], [99.2, 315.0], [99.3, 396.0], [99.4, 396.0], [99.5, 396.0], [99.6, 396.0], [99.7, 457.0], [99.8, 457.0], [99.9, 457.0], [100.0, 457.0]], "isOverall": false, "label": "02 - Projets", "isController": false}, {"data": [[0.0, 60.0], [0.1, 60.0], [0.2, 60.0], [0.3, 60.0], [0.4, 60.0], [0.5, 60.0], [0.6, 60.0], [0.7, 60.0], [0.8, 60.0], [0.9, 60.0], [1.0, 60.0], [1.1, 60.0], [1.2, 60.0], [1.3, 60.0], [1.4, 60.0], [1.5, 60.0], [1.6, 60.0], [1.7, 60.0], [1.8, 60.0], [1.9, 60.0], [2.0, 60.0], [2.1, 60.0], [2.2, 60.0], [2.3, 60.0], [2.4, 60.0], [2.5, 60.0], [2.6, 60.0], [2.7, 60.0], [2.8, 60.0], [2.9, 60.0], [3.0, 60.0], [3.1, 60.0], [3.2, 60.0], [3.3, 60.0], [3.4, 60.0], [3.5, 60.0], [3.6, 60.0], [3.7, 60.0], [3.8, 60.0], [3.9, 60.0], [4.0, 60.0], [4.1, 60.0], [4.2, 60.0], [4.3, 60.0], [4.4, 60.0], [4.5, 60.0], [4.6, 60.0], [4.7, 60.0], [4.8, 60.0], [4.9, 60.0], [5.0, 60.0], [5.1, 60.0], [5.2, 60.0], [5.3, 60.0], [5.4, 60.0], [5.5, 60.0], [5.6, 60.0], [5.7, 60.0], [5.8, 60.0], [5.9, 60.0], [6.0, 60.0], [6.1, 60.0], [6.2, 60.0], [6.3, 60.0], [6.4, 60.0], [6.5, 60.0], [6.6, 60.0], [6.7, 61.0], [6.8, 61.0], [6.9, 61.0], [7.0, 61.0], [7.1, 61.0], [7.2, 61.0], [7.3, 61.0], [7.4, 61.0], [7.5, 61.0], [7.6, 61.0], [7.7, 61.0], [7.8, 61.0], [7.9, 61.0], [8.0, 61.0], [8.1, 61.0], [8.2, 61.0], [8.3, 61.0], [8.4, 61.0], [8.5, 61.0], [8.6, 61.0], [8.7, 61.0], [8.8, 61.0], [8.9, 61.0], [9.0, 61.0], [9.1, 61.0], [9.2, 61.0], [9.3, 61.0], [9.4, 61.0], [9.5, 61.0], [9.6, 61.0], [9.7, 61.0], [9.8, 61.0], [9.9, 61.0], [10.0, 62.0], [10.1, 62.0], [10.2, 62.0], [10.3, 62.0], [10.4, 62.0], [10.5, 62.0], [10.6, 62.0], [10.7, 62.0], [10.8, 62.0], [10.9, 62.0], [11.0, 62.0], [11.1, 62.0], [11.2, 62.0], [11.3, 62.0], [11.4, 62.0], [11.5, 62.0], [11.6, 62.0], [11.7, 62.0], [11.8, 62.0], [11.9, 62.0], [12.0, 62.0], [12.1, 62.0], [12.2, 62.0], [12.3, 62.0], [12.4, 62.0], [12.5, 62.0], [12.6, 62.0], [12.7, 62.0], [12.8, 62.0], [12.9, 62.0], [13.0, 62.0], [13.1, 62.0], [13.2, 62.0], [13.3, 62.0], [13.4, 63.0], [13.5, 63.0], [13.6, 63.0], [13.7, 63.0], [13.8, 63.0], [13.9, 63.0], [14.0, 63.0], [14.1, 63.0], [14.2, 63.0], [14.3, 63.0], [14.4, 63.0], [14.5, 63.0], [14.6, 63.0], [14.7, 63.0], [14.8, 63.0], [14.9, 63.0], [15.0, 63.0], [15.1, 63.0], [15.2, 63.0], [15.3, 63.0], [15.4, 63.0], [15.5, 63.0], [15.6, 63.0], [15.7, 63.0], [15.8, 63.0], [15.9, 63.0], [16.0, 63.0], [16.1, 63.0], [16.2, 63.0], [16.3, 63.0], [16.4, 63.0], [16.5, 63.0], [16.6, 63.0], [16.7, 63.0], [16.8, 63.0], [16.9, 63.0], [17.0, 63.0], [17.1, 63.0], [17.2, 63.0], [17.3, 63.0], [17.4, 63.0], [17.5, 63.0], [17.6, 63.0], [17.7, 63.0], [17.8, 63.0], [17.9, 63.0], [18.0, 63.0], [18.1, 63.0], [18.2, 63.0], [18.3, 63.0], [18.4, 63.0], [18.5, 63.0], [18.6, 63.0], [18.7, 63.0], [18.8, 63.0], [18.9, 63.0], [19.0, 63.0], [19.1, 63.0], [19.2, 63.0], [19.3, 63.0], [19.4, 63.0], [19.5, 63.0], [19.6, 63.0], [19.7, 63.0], [19.8, 63.0], [19.9, 63.0], [20.0, 66.0], [20.1, 66.0], [20.2, 66.0], [20.3, 66.0], [20.4, 66.0], [20.5, 66.0], [20.6, 66.0], [20.7, 66.0], [20.8, 66.0], [20.9, 66.0], [21.0, 66.0], [21.1, 66.0], [21.2, 66.0], [21.3, 66.0], [21.4, 66.0], [21.5, 66.0], [21.6, 66.0], [21.7, 66.0], [21.8, 66.0], [21.9, 66.0], [22.0, 66.0], [22.1, 66.0], [22.2, 66.0], [22.3, 66.0], [22.4, 66.0], [22.5, 66.0], [22.6, 66.0], [22.7, 66.0], [22.8, 66.0], [22.9, 66.0], [23.0, 66.0], [23.1, 66.0], [23.2, 66.0], [23.3, 66.0], [23.4, 66.0], [23.5, 66.0], [23.6, 66.0], [23.7, 66.0], [23.8, 66.0], [23.9, 66.0], [24.0, 66.0], [24.1, 66.0], [24.2, 66.0], [24.3, 66.0], [24.4, 66.0], [24.5, 66.0], [24.6, 66.0], [24.7, 66.0], [24.8, 66.0], [24.9, 66.0], [25.0, 66.0], [25.1, 66.0], [25.2, 66.0], [25.3, 66.0], [25.4, 66.0], [25.5, 66.0], [25.6, 66.0], [25.7, 66.0], [25.8, 66.0], [25.9, 66.0], [26.0, 66.0], [26.1, 66.0], [26.2, 66.0], [26.3, 66.0], [26.4, 66.0], [26.5, 66.0], [26.6, 66.0], [26.7, 69.0], [26.8, 69.0], [26.9, 69.0], [27.0, 69.0], [27.1, 69.0], [27.2, 69.0], [27.3, 69.0], [27.4, 69.0], [27.5, 69.0], [27.6, 69.0], [27.7, 69.0], [27.8, 69.0], [27.9, 69.0], [28.0, 69.0], [28.1, 69.0], [28.2, 69.0], [28.3, 69.0], [28.4, 69.0], [28.5, 69.0], [28.6, 69.0], [28.7, 69.0], [28.8, 69.0], [28.9, 69.0], [29.0, 69.0], [29.1, 69.0], [29.2, 69.0], [29.3, 69.0], [29.4, 69.0], [29.5, 69.0], [29.6, 69.0], [29.7, 69.0], [29.8, 69.0], [29.9, 69.0], [30.0, 70.0], [30.1, 70.0], [30.2, 70.0], [30.3, 70.0], [30.4, 70.0], [30.5, 70.0], [30.6, 70.0], [30.7, 70.0], [30.8, 70.0], [30.9, 70.0], [31.0, 70.0], [31.1, 70.0], [31.2, 70.0], [31.3, 70.0], [31.4, 70.0], [31.5, 70.0], [31.6, 70.0], [31.7, 70.0], [31.8, 70.0], [31.9, 70.0], [32.0, 70.0], [32.1, 70.0], [32.2, 70.0], [32.3, 70.0], [32.4, 70.0], [32.5, 70.0], [32.6, 70.0], [32.7, 70.0], [32.8, 70.0], [32.9, 70.0], [33.0, 70.0], [33.1, 70.0], [33.2, 70.0], [33.3, 70.0], [33.4, 70.0], [33.5, 70.0], [33.6, 70.0], [33.7, 70.0], [33.8, 70.0], [33.9, 70.0], [34.0, 70.0], [34.1, 70.0], [34.2, 70.0], [34.3, 70.0], [34.4, 70.0], [34.5, 70.0], [34.6, 70.0], [34.7, 70.0], [34.8, 70.0], [34.9, 70.0], [35.0, 70.0], [35.1, 70.0], [35.2, 70.0], [35.3, 70.0], [35.4, 70.0], [35.5, 70.0], [35.6, 70.0], [35.7, 70.0], [35.8, 70.0], [35.9, 70.0], [36.0, 70.0], [36.1, 70.0], [36.2, 70.0], [36.3, 70.0], [36.4, 70.0], [36.5, 70.0], [36.6, 70.0], [36.7, 72.0], [36.8, 72.0], [36.9, 72.0], [37.0, 72.0], [37.1, 72.0], [37.2, 72.0], [37.3, 72.0], [37.4, 72.0], [37.5, 72.0], [37.6, 72.0], [37.7, 72.0], [37.8, 72.0], [37.9, 72.0], [38.0, 72.0], [38.1, 72.0], [38.2, 72.0], [38.3, 72.0], [38.4, 72.0], [38.5, 72.0], [38.6, 72.0], [38.7, 72.0], [38.8, 72.0], [38.9, 72.0], [39.0, 72.0], [39.1, 72.0], [39.2, 72.0], [39.3, 72.0], [39.4, 72.0], [39.5, 72.0], [39.6, 72.0], [39.7, 72.0], [39.8, 72.0], [39.9, 72.0], [40.0, 75.0], [40.1, 75.0], [40.2, 75.0], [40.3, 75.0], [40.4, 75.0], [40.5, 75.0], [40.6, 75.0], [40.7, 75.0], [40.8, 75.0], [40.9, 75.0], [41.0, 75.0], [41.1, 75.0], [41.2, 75.0], [41.3, 75.0], [41.4, 75.0], [41.5, 75.0], [41.6, 75.0], [41.7, 75.0], [41.8, 75.0], [41.9, 75.0], [42.0, 75.0], [42.1, 75.0], [42.2, 75.0], [42.3, 75.0], [42.4, 75.0], [42.5, 75.0], [42.6, 75.0], [42.7, 75.0], [42.8, 75.0], [42.9, 75.0], [43.0, 75.0], [43.1, 75.0], [43.2, 75.0], [43.3, 75.0], [43.4, 78.0], [43.5, 78.0], [43.6, 78.0], [43.7, 78.0], [43.8, 78.0], [43.9, 78.0], [44.0, 78.0], [44.1, 78.0], [44.2, 78.0], [44.3, 78.0], [44.4, 78.0], [44.5, 78.0], [44.6, 78.0], [44.7, 78.0], [44.8, 78.0], [44.9, 78.0], [45.0, 78.0], [45.1, 78.0], [45.2, 78.0], [45.3, 78.0], [45.4, 78.0], [45.5, 78.0], [45.6, 78.0], [45.7, 78.0], [45.8, 78.0], [45.9, 78.0], [46.0, 78.0], [46.1, 78.0], [46.2, 78.0], [46.3, 78.0], [46.4, 78.0], [46.5, 78.0], [46.6, 78.0], [46.7, 81.0], [46.8, 81.0], [46.9, 81.0], [47.0, 81.0], [47.1, 81.0], [47.2, 81.0], [47.3, 81.0], [47.4, 81.0], [47.5, 81.0], [47.6, 81.0], [47.7, 81.0], [47.8, 81.0], [47.9, 81.0], [48.0, 81.0], [48.1, 81.0], [48.2, 81.0], [48.3, 81.0], [48.4, 81.0], [48.5, 81.0], [48.6, 81.0], [48.7, 81.0], [48.8, 81.0], [48.9, 81.0], [49.0, 81.0], [49.1, 81.0], [49.2, 81.0], [49.3, 81.0], [49.4, 81.0], [49.5, 81.0], [49.6, 81.0], [49.7, 81.0], [49.8, 81.0], [49.9, 81.0], [50.0, 81.0], [50.1, 86.0], [50.2, 86.0], [50.3, 86.0], [50.4, 86.0], [50.5, 86.0], [50.6, 86.0], [50.7, 86.0], [50.8, 86.0], [50.9, 86.0], [51.0, 86.0], [51.1, 86.0], [51.2, 86.0], [51.3, 86.0], [51.4, 86.0], [51.5, 86.0], [51.6, 86.0], [51.7, 86.0], [51.8, 86.0], [51.9, 86.0], [52.0, 86.0], [52.1, 86.0], [52.2, 86.0], [52.3, 86.0], [52.4, 86.0], [52.5, 86.0], [52.6, 86.0], [52.7, 86.0], [52.8, 86.0], [52.9, 86.0], [53.0, 86.0], [53.1, 86.0], [53.2, 86.0], [53.3, 86.0], [53.4, 87.0], [53.5, 87.0], [53.6, 87.0], [53.7, 87.0], [53.8, 87.0], [53.9, 87.0], [54.0, 87.0], [54.1, 87.0], [54.2, 87.0], [54.3, 87.0], [54.4, 87.0], [54.5, 87.0], [54.6, 87.0], [54.7, 87.0], [54.8, 87.0], [54.9, 87.0], [55.0, 87.0], [55.1, 87.0], [55.2, 87.0], [55.3, 87.0], [55.4, 87.0], [55.5, 87.0], [55.6, 87.0], [55.7, 87.0], [55.8, 87.0], [55.9, 87.0], [56.0, 87.0], [56.1, 87.0], [56.2, 87.0], [56.3, 87.0], [56.4, 87.0], [56.5, 87.0], [56.6, 87.0], [56.7, 88.0], [56.8, 88.0], [56.9, 88.0], [57.0, 88.0], [57.1, 88.0], [57.2, 88.0], [57.3, 88.0], [57.4, 88.0], [57.5, 88.0], [57.6, 88.0], [57.7, 88.0], [57.8, 88.0], [57.9, 88.0], [58.0, 88.0], [58.1, 88.0], [58.2, 88.0], [58.3, 88.0], [58.4, 88.0], [58.5, 88.0], [58.6, 88.0], [58.7, 88.0], [58.8, 88.0], [58.9, 88.0], [59.0, 88.0], [59.1, 88.0], [59.2, 88.0], [59.3, 88.0], [59.4, 88.0], [59.5, 88.0], [59.6, 88.0], [59.7, 88.0], [59.8, 88.0], [59.9, 88.0], [60.0, 88.0], [60.1, 98.0], [60.2, 98.0], [60.3, 98.0], [60.4, 98.0], [60.5, 98.0], [60.6, 98.0], [60.7, 98.0], [60.8, 98.0], [60.9, 98.0], [61.0, 98.0], [61.1, 98.0], [61.2, 98.0], [61.3, 98.0], [61.4, 98.0], [61.5, 98.0], [61.6, 98.0], [61.7, 98.0], [61.8, 98.0], [61.9, 98.0], [62.0, 98.0], [62.1, 98.0], [62.2, 98.0], [62.3, 98.0], [62.4, 98.0], [62.5, 98.0], [62.6, 98.0], [62.7, 98.0], [62.8, 98.0], [62.9, 98.0], [63.0, 98.0], [63.1, 98.0], [63.2, 98.0], [63.3, 98.0], [63.4, 99.0], [63.5, 99.0], [63.6, 99.0], [63.7, 99.0], [63.8, 99.0], [63.9, 99.0], [64.0, 99.0], [64.1, 99.0], [64.2, 99.0], [64.3, 99.0], [64.4, 99.0], [64.5, 99.0], [64.6, 99.0], [64.7, 99.0], [64.8, 99.0], [64.9, 99.0], [65.0, 99.0], [65.1, 99.0], [65.2, 99.0], [65.3, 99.0], [65.4, 99.0], [65.5, 99.0], [65.6, 99.0], [65.7, 99.0], [65.8, 99.0], [65.9, 99.0], [66.0, 99.0], [66.1, 99.0], [66.2, 99.0], [66.3, 99.0], [66.4, 99.0], [66.5, 99.0], [66.6, 99.0], [66.7, 104.0], [66.8, 104.0], [66.9, 104.0], [67.0, 104.0], [67.1, 104.0], [67.2, 104.0], [67.3, 104.0], [67.4, 104.0], [67.5, 104.0], [67.6, 104.0], [67.7, 104.0], [67.8, 104.0], [67.9, 104.0], [68.0, 104.0], [68.1, 104.0], [68.2, 104.0], [68.3, 104.0], [68.4, 104.0], [68.5, 104.0], [68.6, 104.0], [68.7, 104.0], [68.8, 104.0], [68.9, 104.0], [69.0, 104.0], [69.1, 104.0], [69.2, 104.0], [69.3, 104.0], [69.4, 104.0], [69.5, 104.0], [69.6, 104.0], [69.7, 104.0], [69.8, 104.0], [69.9, 104.0], [70.0, 104.0], [70.1, 118.0], [70.2, 118.0], [70.3, 118.0], [70.4, 118.0], [70.5, 118.0], [70.6, 118.0], [70.7, 118.0], [70.8, 118.0], [70.9, 118.0], [71.0, 118.0], [71.1, 118.0], [71.2, 118.0], [71.3, 118.0], [71.4, 118.0], [71.5, 118.0], [71.6, 118.0], [71.7, 118.0], [71.8, 118.0], [71.9, 118.0], [72.0, 118.0], [72.1, 118.0], [72.2, 118.0], [72.3, 118.0], [72.4, 118.0], [72.5, 118.0], [72.6, 118.0], [72.7, 118.0], [72.8, 118.0], [72.9, 118.0], [73.0, 118.0], [73.1, 118.0], [73.2, 118.0], [73.3, 118.0], [73.4, 123.0], [73.5, 123.0], [73.6, 123.0], [73.7, 123.0], [73.8, 123.0], [73.9, 123.0], [74.0, 123.0], [74.1, 123.0], [74.2, 123.0], [74.3, 123.0], [74.4, 123.0], [74.5, 123.0], [74.6, 123.0], [74.7, 123.0], [74.8, 123.0], [74.9, 123.0], [75.0, 123.0], [75.1, 123.0], [75.2, 123.0], [75.3, 123.0], [75.4, 123.0], [75.5, 123.0], [75.6, 123.0], [75.7, 123.0], [75.8, 123.0], [75.9, 123.0], [76.0, 123.0], [76.1, 123.0], [76.2, 123.0], [76.3, 123.0], [76.4, 123.0], [76.5, 123.0], [76.6, 123.0], [76.7, 129.0], [76.8, 129.0], [76.9, 129.0], [77.0, 129.0], [77.1, 129.0], [77.2, 129.0], [77.3, 129.0], [77.4, 129.0], [77.5, 129.0], [77.6, 129.0], [77.7, 129.0], [77.8, 129.0], [77.9, 129.0], [78.0, 129.0], [78.1, 129.0], [78.2, 129.0], [78.3, 129.0], [78.4, 129.0], [78.5, 129.0], [78.6, 129.0], [78.7, 129.0], [78.8, 129.0], [78.9, 129.0], [79.0, 129.0], [79.1, 129.0], [79.2, 129.0], [79.3, 129.0], [79.4, 129.0], [79.5, 129.0], [79.6, 129.0], [79.7, 129.0], [79.8, 129.0], [79.9, 129.0], [80.0, 144.0], [80.1, 144.0], [80.2, 144.0], [80.3, 144.0], [80.4, 144.0], [80.5, 144.0], [80.6, 144.0], [80.7, 144.0], [80.8, 144.0], [80.9, 144.0], [81.0, 144.0], [81.1, 144.0], [81.2, 144.0], [81.3, 144.0], [81.4, 144.0], [81.5, 144.0], [81.6, 144.0], [81.7, 144.0], [81.8, 144.0], [81.9, 144.0], [82.0, 144.0], [82.1, 144.0], [82.2, 144.0], [82.3, 144.0], [82.4, 144.0], [82.5, 144.0], [82.6, 144.0], [82.7, 144.0], [82.8, 144.0], [82.9, 144.0], [83.0, 144.0], [83.1, 144.0], [83.2, 144.0], [83.3, 144.0], [83.4, 144.0], [83.5, 144.0], [83.6, 144.0], [83.7, 144.0], [83.8, 144.0], [83.9, 144.0], [84.0, 144.0], [84.1, 144.0], [84.2, 144.0], [84.3, 144.0], [84.4, 144.0], [84.5, 144.0], [84.6, 144.0], [84.7, 144.0], [84.8, 144.0], [84.9, 144.0], [85.0, 144.0], [85.1, 144.0], [85.2, 144.0], [85.3, 144.0], [85.4, 144.0], [85.5, 144.0], [85.6, 144.0], [85.7, 144.0], [85.8, 144.0], [85.9, 144.0], [86.0, 144.0], [86.1, 144.0], [86.2, 144.0], [86.3, 144.0], [86.4, 144.0], [86.5, 144.0], [86.6, 144.0], [86.7, 201.0], [86.8, 201.0], [86.9, 201.0], [87.0, 201.0], [87.1, 201.0], [87.2, 201.0], [87.3, 201.0], [87.4, 201.0], [87.5, 201.0], [87.6, 201.0], [87.7, 201.0], [87.8, 201.0], [87.9, 201.0], [88.0, 201.0], [88.1, 201.0], [88.2, 201.0], [88.3, 201.0], [88.4, 201.0], [88.5, 201.0], [88.6, 201.0], [88.7, 201.0], [88.8, 201.0], [88.9, 201.0], [89.0, 201.0], [89.1, 201.0], [89.2, 201.0], [89.3, 201.0], [89.4, 201.0], [89.5, 201.0], [89.6, 201.0], [89.7, 201.0], [89.8, 201.0], [89.9, 201.0], [90.0, 202.0], [90.1, 202.0], [90.2, 202.0], [90.3, 202.0], [90.4, 202.0], [90.5, 202.0], [90.6, 202.0], [90.7, 202.0], [90.8, 202.0], [90.9, 202.0], [91.0, 202.0], [91.1, 202.0], [91.2, 202.0], [91.3, 202.0], [91.4, 202.0], [91.5, 202.0], [91.6, 202.0], [91.7, 202.0], [91.8, 202.0], [91.9, 202.0], [92.0, 202.0], [92.1, 202.0], [92.2, 202.0], [92.3, 202.0], [92.4, 202.0], [92.5, 202.0], [92.6, 202.0], [92.7, 202.0], [92.8, 202.0], [92.9, 202.0], [93.0, 202.0], [93.1, 202.0], [93.2, 202.0], [93.3, 202.0], [93.4, 267.0], [93.5, 267.0], [93.6, 267.0], [93.7, 267.0], [93.8, 267.0], [93.9, 267.0], [94.0, 267.0], [94.1, 267.0], [94.2, 267.0], [94.3, 267.0], [94.4, 267.0], [94.5, 267.0], [94.6, 267.0], [94.7, 267.0], [94.8, 267.0], [94.9, 267.0], [95.0, 267.0], [95.1, 267.0], [95.2, 267.0], [95.3, 267.0], [95.4, 267.0], [95.5, 267.0], [95.6, 267.0], [95.7, 267.0], [95.8, 267.0], [95.9, 267.0], [96.0, 267.0], [96.1, 267.0], [96.2, 267.0], [96.3, 267.0], [96.4, 267.0], [96.5, 267.0], [96.6, 267.0], [96.7, 323.0], [96.8, 323.0], [96.9, 323.0], [97.0, 323.0], [97.1, 323.0], [97.2, 323.0], [97.3, 323.0], [97.4, 323.0], [97.5, 323.0], [97.6, 323.0], [97.7, 323.0], [97.8, 323.0], [97.9, 323.0], [98.0, 323.0], [98.1, 323.0], [98.2, 323.0], [98.3, 323.0], [98.4, 323.0], [98.5, 323.0], [98.6, 323.0], [98.7, 323.0], [98.8, 323.0], [98.9, 323.0], [99.0, 323.0], [99.1, 323.0], [99.2, 323.0], [99.3, 323.0], [99.4, 323.0], [99.5, 323.0], [99.6, 323.0], [99.7, 323.0], [99.8, 323.0], [99.9, 323.0]], "isOverall": false, "label": "04 - Liste Tâches", "isController": false}, {"data": [[0.0, 57.0], [0.1, 57.0], [0.2, 57.0], [0.3, 57.0], [0.4, 59.0], [0.5, 59.0], [0.6, 59.0], [0.7, 59.0], [0.8, 59.0], [0.9, 59.0], [1.0, 59.0], [1.1, 59.0], [1.2, 59.0], [1.3, 60.0], [1.4, 60.0], [1.5, 60.0], [1.6, 60.0], [1.7, 60.0], [1.8, 60.0], [1.9, 60.0], [2.0, 60.0], [2.1, 60.0], [2.2, 60.0], [2.3, 60.0], [2.4, 60.0], [2.5, 61.0], [2.6, 61.0], [2.7, 61.0], [2.8, 61.0], [2.9, 61.0], [3.0, 61.0], [3.1, 61.0], [3.2, 62.0], [3.3, 62.0], [3.4, 62.0], [3.5, 62.0], [3.6, 62.0], [3.7, 62.0], [3.8, 62.0], [3.9, 62.0], [4.0, 62.0], [4.1, 62.0], [4.2, 62.0], [4.3, 62.0], [4.4, 62.0], [4.5, 62.0], [4.6, 62.0], [4.7, 62.0], [4.8, 62.0], [4.9, 62.0], [5.0, 62.0], [5.1, 62.0], [5.2, 62.0], [5.3, 62.0], [5.4, 62.0], [5.5, 62.0], [5.6, 62.0], [5.7, 62.0], [5.8, 62.0], [5.9, 62.0], [6.0, 63.0], [6.1, 63.0], [6.2, 63.0], [6.3, 63.0], [6.4, 63.0], [6.5, 63.0], [6.6, 63.0], [6.7, 63.0], [6.8, 63.0], [6.9, 63.0], [7.0, 63.0], [7.1, 63.0], [7.2, 63.0], [7.3, 63.0], [7.4, 63.0], [7.5, 63.0], [7.6, 63.0], [7.7, 63.0], [7.8, 63.0], [7.9, 63.0], [8.0, 63.0], [8.1, 63.0], [8.2, 63.0], [8.3, 63.0], [8.4, 63.0], [8.5, 63.0], [8.6, 63.0], [8.7, 63.0], [8.8, 63.0], [8.9, 63.0], [9.0, 63.0], [9.1, 63.0], [9.2, 63.0], [9.3, 63.0], [9.4, 63.0], [9.5, 63.0], [9.6, 63.0], [9.7, 63.0], [9.8, 63.0], [9.9, 63.0], [10.0, 63.0], [10.1, 63.0], [10.2, 63.0], [10.3, 63.0], [10.4, 64.0], [10.5, 64.0], [10.6, 64.0], [10.7, 64.0], [10.8, 64.0], [10.9, 64.0], [11.0, 64.0], [11.1, 64.0], [11.2, 64.0], [11.3, 64.0], [11.4, 64.0], [11.5, 64.0], [11.6, 64.0], [11.7, 64.0], [11.8, 64.0], [11.9, 64.0], [12.0, 64.0], [12.1, 64.0], [12.2, 64.0], [12.3, 64.0], [12.4, 65.0], [12.5, 65.0], [12.6, 65.0], [12.7, 65.0], [12.8, 65.0], [12.9, 65.0], [13.0, 65.0], [13.1, 65.0], [13.2, 65.0], [13.3, 65.0], [13.4, 65.0], [13.5, 65.0], [13.6, 65.0], [13.7, 65.0], [13.8, 65.0], [13.9, 65.0], [14.0, 65.0], [14.1, 65.0], [14.2, 65.0], [14.3, 65.0], [14.4, 65.0], [14.5, 65.0], [14.6, 65.0], [14.7, 65.0], [14.8, 66.0], [14.9, 66.0], [15.0, 66.0], [15.1, 66.0], [15.2, 66.0], [15.3, 66.0], [15.4, 66.0], [15.5, 66.0], [15.6, 66.0], [15.7, 66.0], [15.8, 66.0], [15.9, 66.0], [16.0, 66.0], [16.1, 66.0], [16.2, 66.0], [16.3, 66.0], [16.4, 66.0], [16.5, 67.0], [16.6, 67.0], [16.7, 67.0], [16.8, 67.0], [16.9, 67.0], [17.0, 67.0], [17.1, 67.0], [17.2, 67.0], [17.3, 67.0], [17.4, 67.0], [17.5, 67.0], [17.6, 67.0], [17.7, 67.0], [17.8, 67.0], [17.9, 67.0], [18.0, 67.0], [18.1, 68.0], [18.2, 68.0], [18.3, 68.0], [18.4, 68.0], [18.5, 68.0], [18.6, 68.0], [18.7, 68.0], [18.8, 68.0], [18.9, 68.0], [19.0, 68.0], [19.1, 68.0], [19.2, 68.0], [19.3, 68.0], [19.4, 68.0], [19.5, 68.0], [19.6, 68.0], [19.7, 68.0], [19.8, 68.0], [19.9, 68.0], [20.0, 68.0], [20.1, 68.0], [20.2, 68.0], [20.3, 68.0], [20.4, 68.0], [20.5, 68.0], [20.6, 68.0], [20.7, 68.0], [20.8, 68.0], [20.9, 68.0], [21.0, 68.0], [21.1, 68.0], [21.2, 68.0], [21.3, 69.0], [21.4, 69.0], [21.5, 69.0], [21.6, 69.0], [21.7, 69.0], [21.8, 69.0], [21.9, 69.0], [22.0, 69.0], [22.1, 70.0], [22.2, 70.0], [22.3, 70.0], [22.4, 70.0], [22.5, 70.0], [22.6, 70.0], [22.7, 70.0], [22.8, 70.0], [22.9, 70.0], [23.0, 70.0], [23.1, 70.0], [23.2, 70.0], [23.3, 71.0], [23.4, 71.0], [23.5, 71.0], [23.6, 71.0], [23.7, 71.0], [23.8, 71.0], [23.9, 71.0], [24.0, 71.0], [24.1, 71.0], [24.2, 71.0], [24.3, 71.0], [24.4, 71.0], [24.5, 71.0], [24.6, 71.0], [24.7, 71.0], [24.8, 71.0], [24.9, 71.0], [25.0, 71.0], [25.1, 71.0], [25.2, 71.0], [25.3, 72.0], [25.4, 72.0], [25.5, 72.0], [25.6, 72.0], [25.7, 72.0], [25.8, 72.0], [25.9, 72.0], [26.0, 72.0], [26.1, 72.0], [26.2, 72.0], [26.3, 72.0], [26.4, 72.0], [26.5, 72.0], [26.6, 72.0], [26.7, 72.0], [26.8, 72.0], [26.9, 72.0], [27.0, 72.0], [27.1, 72.0], [27.2, 72.0], [27.3, 73.0], [27.4, 73.0], [27.5, 73.0], [27.6, 73.0], [27.7, 73.0], [27.8, 73.0], [27.9, 73.0], [28.0, 73.0], [28.1, 74.0], [28.2, 74.0], [28.3, 74.0], [28.4, 74.0], [28.5, 74.0], [28.6, 74.0], [28.7, 74.0], [28.8, 74.0], [28.9, 74.0], [29.0, 74.0], [29.1, 74.0], [29.2, 74.0], [29.3, 76.0], [29.4, 76.0], [29.5, 76.0], [29.6, 77.0], [29.7, 77.0], [29.8, 77.0], [29.9, 77.0], [30.0, 78.0], [30.1, 78.0], [30.2, 78.0], [30.3, 78.0], [30.4, 78.0], [30.5, 78.0], [30.6, 78.0], [30.7, 78.0], [30.8, 78.0], [30.9, 78.0], [31.0, 78.0], [31.1, 78.0], [31.2, 78.0], [31.3, 78.0], [31.4, 78.0], [31.5, 78.0], [31.6, 79.0], [31.7, 79.0], [31.8, 79.0], [31.9, 79.0], [32.0, 79.0], [32.1, 79.0], [32.2, 79.0], [32.3, 79.0], [32.4, 79.0], [32.5, 79.0], [32.6, 79.0], [32.7, 79.0], [32.8, 79.0], [32.9, 80.0], [33.0, 80.0], [33.1, 80.0], [33.2, 80.0], [33.3, 80.0], [33.4, 80.0], [33.5, 80.0], [33.6, 80.0], [33.7, 80.0], [33.8, 80.0], [33.9, 80.0], [34.0, 80.0], [34.1, 81.0], [34.2, 81.0], [34.3, 81.0], [34.4, 81.0], [34.5, 81.0], [34.6, 81.0], [34.7, 81.0], [34.8, 81.0], [34.9, 81.0], [35.0, 81.0], [35.1, 81.0], [35.2, 81.0], [35.3, 82.0], [35.4, 82.0], [35.5, 82.0], [35.6, 82.0], [35.7, 82.0], [35.8, 82.0], [35.9, 82.0], [36.0, 82.0], [36.1, 83.0], [36.2, 83.0], [36.3, 83.0], [36.4, 83.0], [36.5, 84.0], [36.6, 84.0], [36.7, 84.0], [36.8, 84.0], [36.9, 84.0], [37.0, 84.0], [37.1, 84.0], [37.2, 85.0], [37.3, 85.0], [37.4, 85.0], [37.5, 85.0], [37.6, 86.0], [37.7, 86.0], [37.8, 86.0], [37.9, 86.0], [38.0, 86.0], [38.1, 86.0], [38.2, 86.0], [38.3, 86.0], [38.4, 86.0], [38.5, 86.0], [38.6, 86.0], [38.7, 86.0], [38.8, 86.0], [38.9, 86.0], [39.0, 86.0], [39.1, 86.0], [39.2, 87.0], [39.3, 87.0], [39.4, 87.0], [39.5, 87.0], [39.6, 87.0], [39.7, 87.0], [39.8, 87.0], [39.9, 87.0], [40.0, 87.0], [40.1, 87.0], [40.2, 87.0], [40.3, 87.0], [40.4, 87.0], [40.5, 88.0], [40.6, 88.0], [40.7, 88.0], [40.8, 88.0], [40.9, 88.0], [41.0, 88.0], [41.1, 88.0], [41.2, 88.0], [41.3, 88.0], [41.4, 88.0], [41.5, 88.0], [41.6, 88.0], [41.7, 89.0], [41.8, 89.0], [41.9, 89.0], [42.0, 89.0], [42.1, 89.0], [42.2, 89.0], [42.3, 89.0], [42.4, 89.0], [42.5, 89.0], [42.6, 89.0], [42.7, 89.0], [42.8, 89.0], [42.9, 89.0], [43.0, 89.0], [43.1, 89.0], [43.2, 89.0], [43.3, 90.0], [43.4, 90.0], [43.5, 90.0], [43.6, 90.0], [43.7, 90.0], [43.8, 90.0], [43.9, 90.0], [44.0, 90.0], [44.1, 90.0], [44.2, 90.0], [44.3, 90.0], [44.4, 90.0], [44.5, 90.0], [44.6, 90.0], [44.7, 90.0], [44.8, 90.0], [44.9, 91.0], [45.0, 91.0], [45.1, 91.0], [45.2, 91.0], [45.3, 91.0], [45.4, 91.0], [45.5, 91.0], [45.6, 91.0], [45.7, 91.0], [45.8, 91.0], [45.9, 91.0], [46.0, 91.0], [46.1, 92.0], [46.2, 92.0], [46.3, 92.0], [46.4, 92.0], [46.5, 92.0], [46.6, 92.0], [46.7, 92.0], [46.8, 92.0], [46.9, 92.0], [47.0, 92.0], [47.1, 92.0], [47.2, 92.0], [47.3, 92.0], [47.4, 92.0], [47.5, 92.0], [47.6, 92.0], [47.7, 92.0], [47.8, 92.0], [47.9, 92.0], [48.0, 92.0], [48.1, 92.0], [48.2, 92.0], [48.3, 92.0], [48.4, 92.0], [48.5, 92.0], [48.6, 92.0], [48.7, 92.0], [48.8, 92.0], [48.9, 92.0], [49.0, 92.0], [49.1, 92.0], [49.2, 92.0], [49.3, 92.0], [49.4, 92.0], [49.5, 92.0], [49.6, 92.0], [49.7, 93.0], [49.8, 93.0], [49.9, 93.0], [50.0, 93.0], [50.1, 93.0], [50.2, 93.0], [50.3, 93.0], [50.4, 93.0], [50.5, 93.0], [50.6, 93.0], [50.7, 93.0], [50.8, 93.0], [50.9, 94.0], [51.0, 94.0], [51.1, 94.0], [51.2, 94.0], [51.3, 94.0], [51.4, 94.0], [51.5, 94.0], [51.6, 94.0], [51.7, 94.0], [51.8, 94.0], [51.9, 94.0], [52.0, 94.0], [52.1, 94.0], [52.2, 94.0], [52.3, 94.0], [52.4, 94.0], [52.5, 94.0], [52.6, 94.0], [52.7, 94.0], [52.8, 94.0], [52.9, 94.0], [53.0, 94.0], [53.1, 94.0], [53.2, 94.0], [53.3, 95.0], [53.4, 95.0], [53.5, 95.0], [53.6, 95.0], [53.7, 95.0], [53.8, 95.0], [53.9, 95.0], [54.0, 95.0], [54.1, 96.0], [54.2, 96.0], [54.3, 96.0], [54.4, 96.0], [54.5, 96.0], [54.6, 96.0], [54.7, 96.0], [54.8, 96.0], [54.9, 96.0], [55.0, 96.0], [55.1, 96.0], [55.2, 96.0], [55.3, 96.0], [55.4, 96.0], [55.5, 96.0], [55.6, 96.0], [55.7, 96.0], [55.8, 96.0], [55.9, 96.0], [56.0, 96.0], [56.1, 96.0], [56.2, 96.0], [56.3, 96.0], [56.4, 96.0], [56.5, 97.0], [56.6, 97.0], [56.7, 97.0], [56.8, 97.0], [56.9, 97.0], [57.0, 97.0], [57.1, 97.0], [57.2, 97.0], [57.3, 97.0], [57.4, 97.0], [57.5, 97.0], [57.6, 97.0], [57.7, 97.0], [57.8, 97.0], [57.9, 97.0], [58.0, 97.0], [58.1, 98.0], [58.2, 98.0], [58.3, 98.0], [58.4, 98.0], [58.5, 98.0], [58.6, 98.0], [58.7, 98.0], [58.8, 98.0], [58.9, 98.0], [59.0, 98.0], [59.1, 98.0], [59.2, 98.0], [59.3, 98.0], [59.4, 98.0], [59.5, 98.0], [59.6, 98.0], [59.7, 98.0], [59.8, 98.0], [59.9, 98.0], [60.0, 98.0], [60.1, 99.0], [60.2, 99.0], [60.3, 99.0], [60.4, 99.0], [60.5, 99.0], [60.6, 99.0], [60.7, 99.0], [60.8, 99.0], [60.9, 99.0], [61.0, 99.0], [61.1, 99.0], [61.2, 99.0], [61.3, 100.0], [61.4, 100.0], [61.5, 100.0], [61.6, 100.0], [61.7, 101.0], [61.8, 101.0], [61.9, 101.0], [62.0, 101.0], [62.1, 101.0], [62.2, 101.0], [62.3, 101.0], [62.4, 101.0], [62.5, 101.0], [62.6, 101.0], [62.7, 101.0], [62.8, 101.0], [62.9, 101.0], [63.0, 101.0], [63.1, 101.0], [63.2, 101.0], [63.3, 102.0], [63.4, 102.0], [63.5, 102.0], [63.6, 102.0], [63.7, 102.0], [63.8, 102.0], [63.9, 102.0], [64.0, 102.0], [64.1, 102.0], [64.2, 102.0], [64.3, 102.0], [64.4, 102.0], [64.5, 103.0], [64.6, 103.0], [64.7, 103.0], [64.8, 103.0], [64.9, 104.0], [65.0, 104.0], [65.1, 104.0], [65.2, 104.0], [65.3, 104.0], [65.4, 104.0], [65.5, 104.0], [65.6, 104.0], [65.7, 104.0], [65.8, 104.0], [65.9, 104.0], [66.0, 104.0], [66.1, 106.0], [66.2, 106.0], [66.3, 106.0], [66.4, 106.0], [66.5, 107.0], [66.6, 107.0], [66.7, 107.0], [66.8, 107.0], [66.9, 107.0], [67.0, 107.0], [67.1, 107.0], [67.2, 107.0], [67.3, 107.0], [67.4, 107.0], [67.5, 107.0], [67.6, 107.0], [67.7, 108.0], [67.8, 108.0], [67.9, 108.0], [68.0, 108.0], [68.1, 109.0], [68.2, 109.0], [68.3, 109.0], [68.4, 109.0], [68.5, 109.0], [68.6, 109.0], [68.7, 109.0], [68.8, 109.0], [68.9, 113.0], [69.0, 113.0], [69.1, 113.0], [69.2, 113.0], [69.3, 114.0], [69.4, 114.0], [69.5, 114.0], [69.6, 114.0], [69.7, 116.0], [69.8, 116.0], [69.9, 116.0], [70.0, 116.0], [70.1, 116.0], [70.2, 116.0], [70.3, 116.0], [70.4, 116.0], [70.5, 117.0], [70.6, 117.0], [70.7, 117.0], [70.8, 117.0], [70.9, 117.0], [71.0, 117.0], [71.1, 117.0], [71.2, 117.0], [71.3, 119.0], [71.4, 119.0], [71.5, 119.0], [71.6, 119.0], [71.7, 120.0], [71.8, 120.0], [71.9, 120.0], [72.0, 120.0], [72.1, 121.0], [72.2, 121.0], [72.3, 121.0], [72.4, 121.0], [72.5, 122.0], [72.6, 122.0], [72.7, 122.0], [72.8, 122.0], [72.9, 123.0], [73.0, 123.0], [73.1, 123.0], [73.2, 123.0], [73.3, 124.0], [73.4, 124.0], [73.5, 124.0], [73.6, 124.0], [73.7, 124.0], [73.8, 124.0], [73.9, 124.0], [74.0, 124.0], [74.1, 125.0], [74.2, 125.0], [74.3, 125.0], [74.4, 125.0], [74.5, 125.0], [74.6, 125.0], [74.7, 125.0], [74.8, 125.0], [74.9, 125.0], [75.0, 125.0], [75.1, 125.0], [75.2, 125.0], [75.3, 126.0], [75.4, 126.0], [75.5, 126.0], [75.6, 126.0], [75.7, 126.0], [75.8, 126.0], [75.9, 126.0], [76.0, 126.0], [76.1, 126.0], [76.2, 126.0], [76.3, 126.0], [76.4, 126.0], [76.5, 126.0], [76.6, 126.0], [76.7, 126.0], [76.8, 126.0], [76.9, 128.0], [77.0, 128.0], [77.1, 128.0], [77.2, 128.0], [77.3, 131.0], [77.4, 131.0], [77.5, 131.0], [77.6, 131.0], [77.7, 131.0], [77.8, 131.0], [77.9, 131.0], [78.0, 131.0], [78.1, 132.0], [78.2, 132.0], [78.3, 132.0], [78.4, 132.0], [78.5, 132.0], [78.6, 132.0], [78.7, 132.0], [78.8, 132.0], [78.9, 133.0], [79.0, 133.0], [79.1, 133.0], [79.2, 133.0], [79.3, 134.0], [79.4, 134.0], [79.5, 134.0], [79.6, 134.0], [79.7, 135.0], [79.8, 135.0], [79.9, 135.0], [80.0, 135.0], [80.1, 136.0], [80.2, 136.0], [80.3, 136.0], [80.4, 136.0], [80.5, 138.0], [80.6, 138.0], [80.7, 138.0], [80.8, 138.0], [80.9, 138.0], [81.0, 138.0], [81.1, 138.0], [81.2, 138.0], [81.3, 142.0], [81.4, 142.0], [81.5, 142.0], [81.6, 142.0], [81.7, 143.0], [81.8, 143.0], [81.9, 143.0], [82.0, 143.0], [82.1, 144.0], [82.2, 144.0], [82.3, 144.0], [82.4, 144.0], [82.5, 146.0], [82.6, 146.0], [82.7, 146.0], [82.8, 146.0], [82.9, 149.0], [83.0, 149.0], [83.1, 149.0], [83.2, 149.0], [83.3, 153.0], [83.4, 153.0], [83.5, 153.0], [83.6, 153.0], [83.7, 155.0], [83.8, 155.0], [83.9, 155.0], [84.0, 155.0], [84.1, 156.0], [84.2, 156.0], [84.3, 156.0], [84.4, 156.0], [84.5, 157.0], [84.6, 157.0], [84.7, 157.0], [84.8, 157.0], [84.9, 157.0], [85.0, 157.0], [85.1, 157.0], [85.2, 157.0], [85.3, 159.0], [85.4, 159.0], [85.5, 159.0], [85.6, 159.0], [85.7, 159.0], [85.8, 159.0], [85.9, 159.0], [86.0, 159.0], [86.1, 160.0], [86.2, 160.0], [86.3, 160.0], [86.4, 160.0], [86.5, 161.0], [86.6, 161.0], [86.7, 161.0], [86.8, 161.0], [86.9, 164.0], [87.0, 164.0], [87.1, 164.0], [87.2, 164.0], [87.3, 164.0], [87.4, 164.0], [87.5, 164.0], [87.6, 164.0], [87.7, 167.0], [87.8, 167.0], [87.9, 167.0], [88.0, 167.0], [88.1, 167.0], [88.2, 167.0], [88.3, 167.0], [88.4, 167.0], [88.5, 168.0], [88.6, 168.0], [88.7, 168.0], [88.8, 168.0], [88.9, 171.0], [89.0, 171.0], [89.1, 171.0], [89.2, 171.0], [89.3, 177.0], [89.4, 177.0], [89.5, 177.0], [89.6, 177.0], [89.7, 179.0], [89.8, 179.0], [89.9, 179.0], [90.0, 179.0], [90.1, 180.0], [90.2, 180.0], [90.3, 180.0], [90.4, 180.0], [90.5, 180.0], [90.6, 180.0], [90.7, 180.0], [90.8, 180.0], [90.9, 180.0], [91.0, 180.0], [91.1, 180.0], [91.2, 180.0], [91.3, 181.0], [91.4, 181.0], [91.5, 181.0], [91.6, 181.0], [91.7, 190.0], [91.8, 190.0], [91.9, 190.0], [92.0, 190.0], [92.1, 201.0], [92.2, 201.0], [92.3, 201.0], [92.4, 201.0], [92.5, 201.0], [92.6, 201.0], [92.7, 201.0], [92.8, 201.0], [92.9, 203.0], [93.0, 203.0], [93.1, 203.0], [93.2, 203.0], [93.3, 203.0], [93.4, 203.0], [93.5, 203.0], [93.6, 203.0], [93.7, 204.0], [93.8, 204.0], [93.9, 204.0], [94.0, 204.0], [94.1, 206.0], [94.2, 206.0], [94.3, 206.0], [94.4, 206.0], [94.5, 207.0], [94.6, 207.0], [94.7, 207.0], [94.8, 207.0], [94.9, 211.0], [95.0, 211.0], [95.1, 211.0], [95.2, 211.0], [95.3, 221.0], [95.4, 221.0], [95.5, 221.0], [95.6, 221.0], [95.7, 224.0], [95.8, 224.0], [95.9, 224.0], [96.0, 224.0], [96.1, 227.0], [96.2, 227.0], [96.3, 227.0], [96.4, 227.0], [96.5, 229.0], [96.6, 229.0], [96.7, 229.0], [96.8, 229.0], [96.9, 232.0], [97.0, 232.0], [97.1, 232.0], [97.2, 232.0], [97.3, 237.0], [97.4, 237.0], [97.5, 237.0], [97.6, 237.0], [97.7, 246.0], [97.8, 246.0], [97.9, 246.0], [98.0, 246.0], [98.1, 248.0], [98.2, 248.0], [98.3, 248.0], [98.4, 248.0], [98.5, 262.0], [98.6, 262.0], [98.7, 262.0], [98.8, 262.0], [98.9, 282.0], [99.0, 282.0], [99.1, 282.0], [99.2, 282.0], [99.3, 302.0], [99.4, 302.0], [99.5, 302.0], [99.6, 302.0], [99.7, 324.0], [99.8, 324.0], [99.9, 324.0], [100.0, 324.0]], "isOverall": false, "label": "01 - Dashboard", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 161.0, "series": [{"data": [[0.0, 17.0], [100.0, 10.0], [200.0, 3.0]], "isOverall": false, "label": "03 - Liste Projets", "isController": false}, {"data": [[0.0, 160.0], [200.0, 15.0], [100.0, 73.0], [400.0, 2.0]], "isOverall": false, "label": "03 - Tâches", "isController": false}, {"data": [[0.0, 19.0], [100.0, 9.0], [200.0, 2.0]], "isOverall": false, "label": "02 - Health Check", "isController": false}, {"data": [[0.0, 16.0], [200.0, 2.0], [100.0, 12.0]], "isOverall": false, "label": "01 - Dashboard Principal", "isController": false}, {"data": [[0.0, 161.0], [300.0, 2.0], [100.0, 68.0], [200.0, 18.0], [400.0, 1.0]], "isOverall": false, "label": "02 - Projets", "isController": false}, {"data": [[0.0, 20.0], [300.0, 1.0], [100.0, 6.0], [200.0, 3.0]], "isOverall": false, "label": "04 - Liste Tâches", "isController": false}, {"data": [[0.0, 153.0], [300.0, 2.0], [100.0, 77.0], [200.0, 18.0]], "isOverall": false, "label": "01 - Dashboard", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 400.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 870.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 870.0, "series": [{"data": [[0.0, 870.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 4.9E-324, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 3.538461538461538, "minX": 1.7506698E12, "maxY": 41.40935672514617, "series": [{"data": [[1.75066986E12, 41.40935672514617], [1.75066992E12, 40.684065934065906], [1.7506698E12, 9.818181818181818], [1.75066998E12, 9.696969696969699]], "isOverall": false, "label": "Utilisateurs Intensifs (Montée en charge)", "isController": false}, {"data": [[1.75066986E12, 9.404040404040408], [1.75066992E12, 3.538461538461538], [1.7506698E12, 4.5]], "isOverall": false, "label": "Utilisateurs Légers (Navigation)", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75066998E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 48.0, "minX": 1.0, "maxY": 289.0, "series": [{"data": [[33.0, 64.0], [37.0, 185.0], [38.0, 63.0], [39.0, 169.0], [43.0, 79.0], [44.0, 68.5], [45.0, 289.0], [48.0, 80.0], [49.0, 63.0], [50.0, 136.0], [53.0, 64.0], [55.0, 164.33333333333334], [54.0, 73.0], [56.0, 190.0], [57.0, 152.0], [58.0, 96.0], [60.0, 106.25], [20.0, 59.0], [24.0, 109.0], [31.0, 57.0]], "isOverall": false, "label": "03 - Liste Projets", "isController": false}, {"data": [[47.49999999999999, 114.53333333333335]], "isOverall": false, "label": "03 - Liste Projets-Aggregated", "isController": false}, {"data": [[2.0, 91.0], [3.0, 62.0], [4.0, 116.0], [5.0, 63.0], [6.0, 59.0], [7.0, 106.0], [8.0, 83.0], [9.0, 108.0], [10.0, 80.0], [11.0, 81.0], [12.0, 140.0], [13.0, 87.0], [14.0, 60.0], [15.0, 74.8], [16.0, 64.0], [17.0, 63.0], [18.0, 60.0], [19.0, 59.0], [20.0, 77.0], [21.0, 64.0], [22.0, 66.0], [23.0, 64.0], [24.0, 125.33333333333333], [25.0, 89.66666666666667], [26.0, 80.66666666666667], [27.0, 83.75], [28.0, 69.66666666666667], [29.0, 62.0], [30.0, 73.0], [31.0, 64.66666666666667], [33.0, 73.66666666666667], [32.0, 79.0], [34.0, 111.75], [35.0, 90.0], [37.0, 121.33333333333333], [36.0, 211.0], [39.0, 119.0], [38.0, 83.2], [40.0, 140.75], [41.0, 83.4], [43.0, 77.5], [42.0, 130.8], [44.0, 152.0], [45.0, 80.6], [46.0, 76.9], [47.0, 88.8], [48.0, 150.5], [49.0, 67.625], [50.0, 102.66666666666667], [51.0, 240.66666666666666], [52.0, 74.6], [53.0, 95.5], [54.0, 82.0], [55.0, 131.3], [56.0, 91.2], [57.0, 101.19999999999999], [58.0, 116.16666666666666], [59.0, 119.0], [60.0, 163.23076923076923], [1.0, 102.0]], "isOverall": false, "label": "03 - Tâches", "isController": false}, {"data": [[42.89999999999997, 102.85599999999998]], "isOverall": false, "label": "03 - Tâches-Aggregated", "isController": false}, {"data": [[34.0, 53.0], [35.0, 155.0], [39.0, 168.0], [40.0, 212.0], [41.0, 53.0], [44.0, 86.5], [49.0, 81.0], [50.0, 161.0], [51.0, 51.0], [13.0, 143.0], [53.0, 50.0], [54.0, 55.0], [55.0, 49.0], [56.0, 53.5], [57.0, 117.0], [58.0, 106.33333333333333], [59.0, 81.0], [60.0, 119.0], [17.0, 74.0], [19.0, 51.0], [26.0, 49.0], [27.0, 49.0], [31.0, 48.0]], "isOverall": false, "label": "02 - Health Check", "isController": false}, {"data": [[44.66666666666666, 94.06666666666668]], "isOverall": false, "label": "02 - Health Check-Aggregated", "isController": false}, {"data": [[35.0, 98.0], [36.0, 107.0], [37.0, 68.0], [10.0, 123.0], [40.0, 98.0], [44.0, 231.0], [46.0, 86.0], [47.0, 64.0], [49.0, 143.0], [51.0, 97.0], [13.0, 101.0], [52.0, 64.0], [53.0, 77.0], [54.0, 99.0], [57.0, 93.0], [59.0, 152.0], [60.0, 82.5], [19.0, 97.0], [21.0, 102.0], [24.0, 102.0], [27.0, 101.0], [7.0, 259.0], [30.0, 150.0]], "isOverall": false, "label": "01 - Dashboard Principal", "isController": false}, {"data": [[41.06666666666666, 107.66666666666667]], "isOverall": false, "label": "01 - Dashboard Principal-Aggregated", "isController": false}, {"data": [[4.0, 58.0], [5.0, 57.0], [6.0, 93.5], [9.0, 77.0], [11.0, 78.0], [13.0, 57.0], [14.0, 83.0], [15.0, 65.0], [16.0, 94.5], [17.0, 174.66666666666666], [18.0, 59.0], [20.0, 73.5], [21.0, 74.4], [22.0, 83.0], [23.0, 76.25], [25.0, 65.5], [26.0, 55.0], [27.0, 66.66666666666667], [28.0, 103.5], [30.0, 103.33333333333333], [31.0, 92.22222222222223], [33.0, 78.5], [32.0, 58.0], [34.0, 105.33333333333333], [35.0, 87.0], [36.0, 159.0], [37.0, 90.33333333333333], [38.0, 80.85714285714286], [39.0, 127.42857142857143], [40.0, 103.0], [41.0, 66.66666666666667], [42.0, 92.75], [43.0, 65.0], [44.0, 187.0], [45.0, 59.5], [46.0, 72.22222222222223], [47.0, 68.11111111111111], [48.0, 66.5], [49.0, 80.0], [50.0, 100.23076923076923], [51.0, 248.76923076923077], [52.0, 80.57142857142857], [53.0, 81.36363636363636], [54.0, 96.57142857142858], [55.0, 116.42857142857143], [56.0, 96.62500000000001], [57.0, 82.41666666666666], [58.0, 118.5], [59.0, 146.37500000000003], [60.0, 123.74999999999997]], "isOverall": false, "label": "02 - Projets", "isController": false}, {"data": [[44.10800000000004, 103.96400000000004]], "isOverall": false, "label": "02 - Projets-Aggregated", "isController": false}, {"data": [[36.0, 70.0], [40.0, 201.0], [43.0, 89.0], [44.0, 202.0], [47.0, 62.0], [48.0, 75.0], [49.0, 92.5], [50.0, 60.0], [53.0, 86.5], [52.0, 88.0], [54.0, 66.5], [55.0, 267.0], [57.0, 63.5], [56.0, 98.0], [59.0, 72.0], [58.0, 129.0], [60.0, 154.25], [27.0, 81.0], [30.0, 133.5]], "isOverall": false, "label": "04 - Liste Tâches", "isController": false}, {"data": [[50.06666666666668, 107.63333333333331]], "isOverall": false, "label": "04 - Liste Tâches-Aggregated", "isController": false}, {"data": [[33.0, 122.5], [34.0, 106.5], [35.0, 169.0], [36.0, 81.5], [37.0, 114.0], [38.0, 85.25], [39.0, 114.75], [40.0, 97.75], [41.0, 101.0], [43.0, 78.0], [42.0, 151.5], [44.0, 171.85714285714286], [45.0, 75.88888888888889], [46.0, 93.25], [47.0, 69.16666666666667], [48.0, 117.0], [49.0, 74.66666666666667], [50.0, 110.52631578947367], [51.0, 163.66666666666669], [52.0, 98.5], [53.0, 110.06666666666668], [54.0, 104.58823529411765], [55.0, 149.0], [56.0, 125.10000000000004], [57.0, 115.12500000000001], [58.0, 87.625], [59.0, 132.25], [60.0, 129.0909090909091], [7.0, 116.5], [9.0, 83.0], [11.0, 91.75], [13.0, 100.0], [15.0, 103.0], [16.0, 95.0], [17.0, 91.25], [20.0, 92.5], [21.0, 76.6], [22.0, 146.0], [23.0, 104.0], [24.0, 169.0], [25.0, 71.33333333333333], [26.0, 85.0], [27.0, 63.5], [28.0, 90.33333333333333], [30.0, 98.0], [31.0, 78.62500000000001]], "isOverall": false, "label": "01 - Dashboard", "isController": false}, {"data": [[44.62, 107.70399999999998]], "isOverall": false, "label": "01 - Dashboard-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 60.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 47.833333333333336, "minX": 1.7506698E12, "maxY": 614476.4833333333, "series": [{"data": [[1.75066986E12, 614476.4833333333], [1.75066992E12, 576729.7], [1.7506698E12, 19293.716666666667], [1.75066998E12, 55199.98333333333]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.75066986E12, 1606.3], [1.75066992E12, 1486.8666666666666], [1.7506698E12, 47.833333333333336], [1.75066998E12, 130.13333333333333]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75066998E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 49.5, "minX": 1.7506698E12, "maxY": 145.0, "series": [{"data": [[1.75066986E12, 120.04], [1.75066992E12, 94.0], [1.7506698E12, 59.0]], "isOverall": false, "label": "03 - Liste Projets", "isController": false}, {"data": [[1.75066986E12, 114.14851485148516], [1.75066992E12, 96.41666666666667], [1.75066998E12, 85.76470588235296]], "isOverall": false, "label": "03 - Tâches", "isController": false}, {"data": [[1.75066986E12, 98.2], [1.75066992E12, 49.5], [1.7506698E12, 89.33333333333333]], "isOverall": false, "label": "02 - Health Check", "isController": false}, {"data": [[1.75066986E12, 103.47999999999999], [1.75066992E12, 63.0], [1.7506698E12, 145.0]], "isOverall": false, "label": "01 - Dashboard Principal", "isController": false}, {"data": [[1.75066986E12, 107.31578947368419], [1.75066992E12, 104.19672131147541], [1.7506698E12, 82.66666666666667], [1.75066998E12, 72.45454545454545]], "isOverall": false, "label": "02 - Projets", "isController": false}, {"data": [[1.75066986E12, 107.66666666666664], [1.75066992E12, 107.5]], "isOverall": false, "label": "04 - Liste Tâches", "isController": false}, {"data": [[1.75066986E12, 116.96062992125984], [1.75066992E12, 98.29090909090911], [1.7506698E12, 104.125], [1.75066998E12, 85.4]], "isOverall": false, "label": "01 - Dashboard", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75066998E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 15.0, "minX": 1.7506698E12, "maxY": 104.5, "series": [{"data": [[1.75066986E12, 81.99999999999999], [1.75066992E12, 55.75], [1.7506698E12, 26.0]], "isOverall": false, "label": "03 - Liste Projets", "isController": false}, {"data": [[1.75066986E12, 77.80198019801978], [1.75066992E12, 61.40151515151516], [1.75066998E12, 51.352941176470594]], "isOverall": false, "label": "03 - Tâches", "isController": false}, {"data": [[1.75066986E12, 58.000000000000014], [1.75066992E12, 15.0], [1.7506698E12, 47.666666666666664]], "isOverall": false, "label": "02 - Health Check", "isController": false}, {"data": [[1.75066986E12, 65.83999999999999], [1.75066992E12, 30.0], [1.7506698E12, 104.5]], "isOverall": false, "label": "01 - Dashboard Principal", "isController": false}, {"data": [[1.75066986E12, 71.22807017543857], [1.75066992E12, 67.91803278688525], [1.7506698E12, 48.0], [1.75066998E12, 38.27272727272727]], "isOverall": false, "label": "02 - Projets", "isController": false}, {"data": [[1.75066986E12, 71.20833333333333], [1.75066992E12, 71.83333333333333]], "isOverall": false, "label": "04 - Liste Tâches", "isController": false}, {"data": [[1.75066986E12, 80.11811023622046], [1.75066992E12, 62.86363636363638], [1.7506698E12, 64.75], [1.75066998E12, 50.2]], "isOverall": false, "label": "01 - Dashboard", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75066998E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.7506698E12, "maxY": 3.75, "series": [{"data": [[1.75066986E12, 0.44], [1.75066992E12, 0.75], [1.7506698E12, 1.0]], "isOverall": false, "label": "03 - Liste Projets", "isController": false}, {"data": [[1.75066986E12, 0.6534653465346533], [1.75066992E12, 0.4696969696969696], [1.75066998E12, 0.4117647058823529]], "isOverall": false, "label": "03 - Tâches", "isController": false}, {"data": [[1.75066986E12, 0.68], [1.75066992E12, 1.0], [1.7506698E12, 1.0]], "isOverall": false, "label": "02 - Health Check", "isController": false}, {"data": [[1.75066986E12, 0.4800000000000001], [1.75066992E12, 0.0], [1.7506698E12, 3.75]], "isOverall": false, "label": "01 - Dashboard Principal", "isController": false}, {"data": [[1.75066986E12, 0.5614035087719302], [1.75066992E12, 0.5], [1.7506698E12, 0.6666666666666666], [1.75066998E12, 0.45454545454545453]], "isOverall": false, "label": "02 - Projets", "isController": false}, {"data": [[1.75066986E12, 0.5833333333333334], [1.75066992E12, 0.6666666666666667]], "isOverall": false, "label": "04 - Liste Tâches", "isController": false}, {"data": [[1.75066986E12, 0.598425196850394], [1.75066992E12, 0.5545454545454543], [1.7506698E12, 1.0], [1.75066998E12, 1.0]], "isOverall": false, "label": "01 - Dashboard", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75066998E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 48.0, "minX": 1.7506698E12, "maxY": 480.0, "series": [{"data": [[1.75066986E12, 323.0], [1.75066992E12, 480.0], [1.7506698E12, 259.0], [1.75066998E12, 140.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.75066986E12, 200.40000000000003], [1.75066992E12, 164.0], [1.7506698E12, 143.0], [1.75066998E12, 107.2]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.75066986E12, 287.73999999999995], [1.75066992E12, 404.57999999999885], [1.7506698E12, 259.0], [1.75066998E12, 140.0]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.75066986E12, 227.0], [1.75066992E12, 212.29999999999956], [1.7506698E12, 259.0], [1.75066998E12, 123.19999999999993]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.75066986E12, 48.0], [1.75066992E12, 49.0], [1.7506698E12, 51.0], [1.75066998E12, 57.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.75066986E12, 93.0], [1.75066992E12, 78.0], [1.7506698E12, 98.0], [1.75066998E12, 82.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75066998E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 70.5, "minX": 1.0, "maxY": 149.5, "series": [{"data": [[2.0, 80.5], [8.0, 73.0], [9.0, 91.0], [10.0, 93.0], [11.0, 93.0], [3.0, 78.0], [12.0, 146.0], [13.0, 146.5], [14.0, 149.5], [1.0, 102.0], [4.0, 79.0], [5.0, 70.5], [6.0, 73.5], [7.0, 92.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 14.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 32.5, "minX": 1.0, "maxY": 111.0, "series": [{"data": [[2.0, 42.0], [8.0, 36.5], [9.0, 56.0], [10.0, 55.5], [11.0, 59.0], [3.0, 42.0], [12.0, 106.5], [13.0, 111.0], [14.0, 111.0], [1.0, 63.0], [4.0, 42.0], [5.0, 32.5], [6.0, 37.0], [7.0, 55.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 14.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 0.31666666666666665, "minX": 1.7506698E12, "maxY": 7.35, "series": [{"data": [[1.75066986E12, 7.35], [1.75066992E12, 6.283333333333333], [1.7506698E12, 0.31666666666666665], [1.75066998E12, 0.55]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75066998E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.31666666666666665, "minX": 1.7506698E12, "maxY": 7.35, "series": [{"data": [[1.75066986E12, 7.35], [1.75066992E12, 6.283333333333333], [1.7506698E12, 0.31666666666666665], [1.75066998E12, 0.55]], "isOverall": false, "label": "200", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75066998E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.7506698E12, "maxY": 2.2, "series": [{"data": [[1.75066986E12, 0.4166666666666667], [1.75066992E12, 0.016666666666666666], [1.7506698E12, 0.06666666666666667]], "isOverall": false, "label": "01 - Dashboard Principal-success", "isController": false}, {"data": [[1.75066986E12, 0.4], [1.75066992E12, 0.1]], "isOverall": false, "label": "04 - Liste Tâches-success", "isController": false}, {"data": [[1.75066986E12, 0.4166666666666667], [1.75066992E12, 0.03333333333333333], [1.7506698E12, 0.05]], "isOverall": false, "label": "02 - Health Check-success", "isController": false}, {"data": [[1.75066986E12, 1.6833333333333333], [1.75066992E12, 2.2], [1.75066998E12, 0.2833333333333333]], "isOverall": false, "label": "03 - Tâches-success", "isController": false}, {"data": [[1.75066986E12, 2.1166666666666667], [1.75066992E12, 1.8333333333333333], [1.7506698E12, 0.13333333333333333], [1.75066998E12, 0.08333333333333333]], "isOverall": false, "label": "01 - Dashboard-success", "isController": false}, {"data": [[1.75066986E12, 1.9], [1.75066992E12, 2.033333333333333], [1.7506698E12, 0.05], [1.75066998E12, 0.18333333333333332]], "isOverall": false, "label": "02 - Projets-success", "isController": false}, {"data": [[1.75066986E12, 0.4166666666666667], [1.75066992E12, 0.06666666666666667], [1.7506698E12, 0.016666666666666666]], "isOverall": false, "label": "03 - Liste Projets-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75066998E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.31666666666666665, "minX": 1.7506698E12, "maxY": 7.35, "series": [{"data": [[1.75066986E12, 7.35], [1.75066992E12, 6.283333333333333], [1.7506698E12, 0.31666666666666665], [1.75066998E12, 0.55]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75066998E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

